package user

import (
	"context"
	"fmt"
	"gofly/app/client/cons"
	"gofly/app/client/dto/crawler"
	"gofly/app/client/entity"
	"gofly/app/client/entity/services"
	assets_mapper "gofly/app/client/mappers/assets"
	"gofly/app/client/validators"
	"gofly/service/assets/workflow"
	"gofly/setting"
	"gofly/utils/gf"
	"gofly/utils/tools/gcfg"
	"gofly/utils/tools/gconv"
	"gofly/utils/tools/gctx"
	"gofly/utils/tools/gstr"
	"strconv"
	"strings"
	"time"

	client "github.com/qihaozhushou/mediacrawler-client"

	"dario.cat/mergo"
)

// 表名和字段名常量定义
var (
	// 表名
	tableUser                   = &[]string{"user"}[0]
	tableDouyinCollects         = &[]string{"douyin_collects"}[0]
	tableDouyinVideoInfo        = &[]string{"douyin_video_info"}[0]
	tableAssets                 = &[]string{"assets"}[0]
	tableUserInboxSourceRelated = &[]string{"user_inbox_source_related"}[0]

	// 字段名
	fieldUUID         = &[]string{"uuid"}[0]
	fieldUserUUID     = &[]string{"user_uuid"}[0]
	fieldCollectId    = &[]string{"collect_id"}[0]
	fieldDouyinCookie = &[]string{"douyin_cookie"}[0]

	// 来源类型
	sourceTypeCollect = &[]string{string(entity.SourceTypeCollect)}[0]
)

type Douyin struct {
	NoNeedLogin []string //忽略登录接口配置-忽略全部传["*"]
	NoNeedAuths []string //忽略RBAC权限认证接口配置-忽略全部传["*"]
}

// createMediaCrawlerClient 创建 MediaCrawler 客户端
func (api *Douyin) createMediaCrawlerClient() (*client.APIClient, error) {
	var gfctx = gctx.New()
	baseUrl, err := gcfg.Instance().Get(gfctx, "mediaCrawler.default.baseUrl")
	if err != nil {
		return nil, fmt.Errorf("获取MediaCrawler配置失败: %w", err)
	}

	baseUrlStr := baseUrl.String()
	if baseUrlStr == "" {
		baseUrlStr = "http://localhost:8000" // 默认地址
	}

	// 创建 OpenAPI 客户端配置
	cfg := client.NewConfiguration()
	cfg.Host = strings.TrimPrefix(strings.TrimPrefix(baseUrlStr, "http://"), "https://")
	if strings.HasPrefix(baseUrlStr, "https://") {
		cfg.Scheme = "https"
	} else {
		cfg.Scheme = "http"
	}

	apiClient := client.NewAPIClient(cfg)
	return apiClient, nil
}

func init() {
	fpath := Douyin{NoNeedLogin: []string{}, NoNeedAuths: []string{"syncCollects", "bindDouyinCookie", "getSyncRecords", "getSyncStats", "getSyncRecordRelated", "getSyncRecordRelatedList", "getVideoDetail", "getVideoRelatedList", "getVideoRelatedStats", "addVideoToAssets", "getAuthorVideos"}}
	gf.Register(&fpath, fpath)
}

func (api *Douyin) BindDouyinCookie(c *gf.GinCtx) {
	param, _ := gf.RequestParam(c)
	getuser, _ := c.Get("user")
	userOBJ := getuser.(gf.UserObj)
	userUUID := string(userOBJ.UserUUID)

	var dto *crawler.CommonCookieDTO
	err := gconv.Struct(param, &dto)
	if err != nil {
		gf.Failed().SetMsg("参数不正确").Regin(c)
		return
	}

	// 创建 MediaCrawler 客户端
	apiClient, err := api.createMediaCrawlerClient()
	if err != nil {
		gf.Failed().SetMsg("创建MediaCrawler客户端失败: " + err.Error()).Regin(c)
		return
	}

	// 创建上下文
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// 调用 Cookie 验证 API
	resp, httpResp, err := apiClient.CookiesAPIAPI.ValidateCookies(ctx).
		Cookies(dto.Cookie).
		Execute()

	if err != nil {
		gf.Log().Error(context.Background(), "Cookie验证API调用失败", gf.Map{
			"error":  err.Error(),
			"cookie": dto.Cookie,
		})
		gf.Failed().SetMsg("cookie 检测失败: " + err.Error()).Regin(c)
		return
	}

	gf.Log().Info(context.Background(), "Cookie验证API调用成功", gf.Map{
		"http_status": httpResp.StatusCode,
		"response":    resp,
	})

	// 检查响应结果
	if resp == nil {
		gf.Failed().SetMsg("cookie 检测失败: 响应数据为空").Regin(c)
		return
	}

	// 解析响应数据
	valid := resp.GetValid()
	message := resp.GetMessage()

	if !valid {
		gf.Failed().SetMsg(message).Regin(c)
		return
	}

	// Cookie 验证成功，更新数据库
	gf.Model(*tableUser).
		Where(*fieldUUID, userUUID).
		Data(map[string]any{
			*fieldDouyinCookie: dto.Cookie,
		}).Update()

	gf.Success().SetMsg("cookie 绑定成功").SetData(gf.Map{
		"valid":   valid,
		"message": message,
	}).Regin(c)
}

func handleSyncCollects(user entity.User) []entity.DouyinCollect {
	var gfctx = gctx.New()
	usedList := make([]entity.DouyinCollect, 0)

	// 创建 MediaCrawler 客户端
	apiClient, err := (&Douyin{}).createMediaCrawlerClient()
	if err != nil {
		gf.Log().Error(gfctx, "创建MediaCrawler客户端失败", gf.Map{
			"error": err.Error(),
			"user":  user.UUID,
		})
		return usedList
	}

	// 创建上下文
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// 调用获取收藏夹信息 API
	resp, httpResp, err := apiClient.APIAPI.GetSelfAwemeCollectionRpcWithCookies(ctx).
		Cookies(user.DouyinCookie).
		Execute()

	if err != nil {
		gf.Log().Error(gfctx, "获取收藏夹信息失败", gf.Map{
			"error": err.Error(),
			"user":  user.UUID,
		})
		return usedList
	}

	if httpResp.StatusCode != 200 {
		gf.Log().Error(gfctx, "获取收藏夹信息HTTP状态异常", gf.Map{
			"status_code": httpResp.StatusCode,
			"user":        user.UUID,
		})
		return usedList
	}

	// 使用强类型处理响应
	if resp == nil {
		gf.Log().Error(gfctx, "响应数据为空", gf.Map{
			"user": user.UUID,
		})
		return usedList
	}

	// 记录响应信息
	gf.Log().Info(gfctx, "获取收藏夹信息成功", gf.Map{
		"user":        user.UUID,
		"status_code": httpResp.StatusCode,
		"resp_type":   fmt.Sprintf("%T", resp),
	})

	// 获取数据
	collectsList := resp.GetCollectsList()

	// 处理收藏夹数据
	for _, collectItem := range collectsList {
		// 检查收藏夹名称
		collectsName := collectItem.GetCollectsName()
		if !gstr.Contains(collectsName, "素材") {
			continue
		}

		// 转换为实体对象
		var douyinCollect entity.DouyinCollect

		// 手动映射字段
		// 处理 CollectsId（可能是 int64 或 string）
		collectsId := collectItem.GetCollectsId()
		if collectsId.Int64 != nil {
			douyinCollect.CollectsID = *collectsId.Int64
			douyinCollect.CollectsIDStr = gconv.String(*collectsId.Int64)
		} else if collectsId.String != nil {
			douyinCollect.CollectsID = gconv.Int64(*collectsId.String)
			douyinCollect.CollectsIDStr = *collectsId.String
		}

		douyinCollect.CollectsName = collectItem.GetCollectsName()
		douyinCollect.TotalNumber = int(collectItem.GetCount())

		// 设置默认值（因为新的 API 结构中没有这些字段）
		douyinCollect.CreateTime = 0
		douyinCollect.AppID = 0
		douyinCollect.FollowStatus = 0
		douyinCollect.FollowedCount = 0
		douyinCollect.IsNormalStatus = true
		douyinCollect.ItemType = 0
		douyinCollect.LastCollectTime = 0
		douyinCollect.PlayCount = 0
		douyinCollect.States = 0
		douyinCollect.Status = 0
		douyinCollect.SystemType = 0

		// 设置用户相关字段的默认值
		douyinCollect.UserID = 0
		douyinCollect.UserIDStr = ""

		// 处理封面信息
		if coverSchema, ok := collectItem.GetCollectsCoverOk(); ok && coverSchema != nil {
			// coverSchema 是 map[string]interface{}，需要手动解析
			if urlListInterface, exists := coverSchema["url_list"]; exists {
				if urlList, ok := urlListInterface.([]interface{}); ok && len(urlList) > 0 {
					if url, ok := urlList[0].(string); ok {
						douyinCollect.CollectsCover = url
					}
				}
			} else if uri, exists := coverSchema["uri"]; exists {
				if uriStr, ok := uri.(string); ok {
					douyinCollect.CollectsCover = uriStr
				}
			}
		}

		douyinCollect.UserUUID = user.UUID
		usedList = append(usedList, douyinCollect)
	}

	if len(usedList) == 0 {
		gf.Log().Info(gfctx, "未找到包含'素材'的收藏夹", gf.Map{
			"user": user.UUID,
		})
		return usedList
	}

	// 保存到数据库
	_, err = gf.Model(*tableDouyinCollects).Data(usedList).Save()
	if err != nil {
		gf.Log().Error(gfctx, "保存收藏夹数据失败", gf.Map{
			"error": err.Error(),
			"user":  user.UUID,
			"count": len(usedList),
		})
		return usedList
	}

	// 同时保存到用户收件箱来源关联表
	if len(usedList) > 0 {
		gf.Log().Info(gfctx, "准备保存收藏夹到用户收件箱来源关联表", gf.Map{
			"user":  user.UUID,
			"count": len(usedList),
		})

		err = saveCollectsToInboxSourceRelated(user.UUID, usedList, gfctx)
		if err != nil {
			gf.Log().Error(gfctx, "保存收藏夹到用户收件箱来源关联表失败", gf.Map{
				"error": err.Error(),
				"user":  user.UUID,
				"count": len(usedList),
			})
			// 这里不返回错误，因为主要数据已经保存成功，只是关联数据保存失败
		} else {
			gf.Log().Info(gfctx, "保存收藏夹到用户收件箱来源关联表成功", gf.Map{
				"user":  user.UUID,
				"count": len(usedList),
			})
		}
	} else {
		gf.Log().Info(gfctx, "没有收藏夹数据需要保存到用户收件箱来源关联表", gf.Map{
			"user": user.UUID,
		})
	}

	gf.Log().Info(gfctx, "同步收藏夹成功", gf.Map{
		"user":  user.UUID,
		"count": len(usedList),
	})

	return usedList
}

// checkCollectVideoRelatedExists 检查收藏夹是否有对应的视频关联记录
func checkCollectVideoRelatedExists(userUUID, collectID string, gfctx context.Context) (bool, error) {
	gf.Log().Info(gfctx, "检查收藏夹视频关联记录", gf.Map{
		"user_uuid":  userUUID,
		"collect_id": collectID,
	})

	// 查询该收藏夹是否有视频关联记录
	// 使用 source_type='COLLECT' 和 source_id=collectID 进行查询
	count, err := gf.Model("user_inbox_video_related").
		Where("user_uuid", userUUID).
		Where("source_id", collectID).
		Where("source_type", string(entity.SourceTypeCollect)).
		Where("deleted_at IS NULL").
		Count()

	if err != nil {
		gf.Log().Error(gfctx, "查询收藏夹视频关联记录失败", gf.Map{
			"error":      err.Error(),
			"user_uuid":  userUUID,
			"collect_id": collectID,
		})
		return false, err
	}

	hasVideoRelated := count > 0
	gf.Log().Info(gfctx, "收藏夹视频关联记录检查完成", gf.Map{
		"user_uuid":         userUUID,
		"collect_id":        collectID,
		"video_count":       count,
		"has_video_related": hasVideoRelated,
	})

	return hasVideoRelated, nil
}

// saveCollectsToInboxSourceRelated 将收藏夹保存到用户收件箱来源关联表
func saveCollectsToInboxSourceRelated(userUUID string, collectList []entity.DouyinCollect, gfctx context.Context) error {
	gf.Log().Info(gfctx, "开始保存收藏夹到用户收件箱来源关联表", gf.Map{
		"user_uuid":     userUUID,
		"collect_count": len(collectList),
		"source_type":   *sourceTypeCollect,
	})

	if len(collectList) == 0 {
		gf.Log().Info(gfctx, "收藏夹列表为空，跳过保存操作", gf.Map{
			"user_uuid": userUUID,
		})
		return nil
	}

	// 创建用户收件箱来源关联服务
	sourceRelatedService := services.NewUserInboxSourceRelatedService()
	gf.Log().Info(gfctx, "用户收件箱来源关联服务创建成功", gf.Map{
		"user_uuid": userUUID,
	})

	// 准备批量插入的记录
	var sourceRelatedRecords []*entity.UserInboxSourceRelated
	var skippedCount int
	var errorCount int

	for i, collect := range collectList {
		gf.Log().Info(gfctx, "处理收藏夹记录", gf.Map{
			"user_uuid":    userUUID,
			"index":        i + 1,
			"total":        len(collectList),
			"collect_id":   collect.CollectsID,
			"collect_name": collect.CollectsName,
		})

		// 检查是否已存在该收藏夹的关联记录
		gf.Log().Info(gfctx, "检查收藏夹关联记录是否已存在", gf.Map{
			"user_uuid":   userUUID,
			"collect_id":  collect.CollectsID,
			"source_type": *sourceTypeCollect,
		})

		existing, err := sourceRelatedService.GetByUserKeywordAndType(userUUID, gconv.String(collect.CollectsID), *sourceTypeCollect)
		if err != nil {
			// 真正的错误才记录并跳过
			errorCount++
			gf.Log().Warning(gfctx, "检查收藏夹关联记录失败", gf.Map{
				"error":        err.Error(),
				"user_uuid":    userUUID,
				"collect_id":   collect.CollectsID,
				"collect_name": collect.CollectsName,
				"error_count":  errorCount,
			})
			continue
		}

		// 如果已存在，还需要检查是否有对应的视频关联记录
		if existing != nil {
			// 检查 user_inbox_video_related 表中是否有对应的视频数据
			hasVideoRelated, err := checkCollectVideoRelatedExists(userUUID, gconv.String(collect.CollectsID), gfctx)
			if err != nil {
				gf.Log().Warning(gfctx, "检查收藏夹视频关联记录失败", gf.Map{
					"error":        err.Error(),
					"user_uuid":    userUUID,
					"collect_id":   collect.CollectsID,
					"collect_name": collect.CollectsName,
				})
				// 检查失败时，为了安全起见，跳过该收藏夹
				skippedCount++
				continue
			}

			if hasVideoRelated {
				skippedCount++
				gf.Log().Info(gfctx, "收藏夹关联记录和视频关联记录都已存在，跳过", gf.Map{
					"user_uuid":     userUUID,
					"collect_id":    collect.CollectsID,
					"collect_name":  collect.CollectsName,
					"existing_uuid": existing.UUID,
					"skipped_count": skippedCount,
				})
				continue
			} else {
				gf.Log().Info(gfctx, "收藏夹关联记录存在但缺少视频关联记录，需要重新同步", gf.Map{
					"user_uuid":     userUUID,
					"collect_id":    collect.CollectsID,
					"collect_name":  collect.CollectsName,
					"existing_uuid": existing.UUID,
				})
				// 不跳过，继续处理该收藏夹
			}
		}

		gf.Log().Info(gfctx, "收藏夹关联记录不存在，准备创建新记录", gf.Map{
			"user_uuid":    userUUID,
			"collect_id":   collect.CollectsID,
			"collect_name": collect.CollectsName,
		})

		// 创建新的关联记录
		sourceRelated := &entity.UserInboxSourceRelated{
			UserUUID:   userUUID,
			SourceId:   gconv.String(collect.CollectsID),
			SourceType: entity.SourceTypeCollect,
		}

		gf.Log().Info(gfctx, "关联记录实体创建完成，执行创建前钩子", gf.Map{
			"user_uuid":   userUUID,
			"collect_id":  collect.CollectsID,
			"source_id":   sourceRelated.SourceId,
			"source_type": sourceRelated.SourceType,
		})

		// 执行创建前钩子
		if err := sourceRelated.BeforeCreate(); err != nil {
			errorCount++
			gf.Log().Warning(gfctx, "收藏夹关联记录创建前钩子失败", gf.Map{
				"error":        err.Error(),
				"user_uuid":    userUUID,
				"collect_id":   collect.CollectsID,
				"collect_name": collect.CollectsName,
				"error_count":  errorCount,
			})
			continue
		}

		gf.Log().Info(gfctx, "关联记录创建前钩子执行成功，添加到批量插入列表", gf.Map{
			"user_uuid":   userUUID,
			"collect_id":  collect.CollectsID,
			"record_uuid": sourceRelated.UUID,
		})

		sourceRelatedRecords = append(sourceRelatedRecords, sourceRelated)
	}

	gf.Log().Info(gfctx, "收藏夹关联记录处理完成", gf.Map{
		"user_uuid":         userUUID,
		"total_processed":   len(collectList),
		"records_to_insert": len(sourceRelatedRecords),
		"skipped_count":     skippedCount,
		"error_count":       errorCount,
	})

	// 批量插入关联记录
	if len(sourceRelatedRecords) > 0 {
		gf.Log().Info(gfctx, "开始批量插入收藏夹关联记录", gf.Map{
			"user_uuid":     userUUID,
			"records_count": len(sourceRelatedRecords),
			"table_name":    *tableUserInboxSourceRelated,
		})

		_, err := gf.Model(*tableUserInboxSourceRelated).Insert(sourceRelatedRecords)
		if err != nil {
			gf.Log().Error(gfctx, "批量插入收藏夹关联记录失败", gf.Map{
				"error":         err.Error(),
				"user_uuid":     userUUID,
				"records_count": len(sourceRelatedRecords),
				"table_name":    *tableUserInboxSourceRelated,
			})
			return fmt.Errorf("批量插入收藏夹关联记录失败: %w", err)
		}

		gf.Log().Info(gfctx, "收藏夹关联记录保存成功", gf.Map{
			"user_uuid":     userUUID,
			"records_count": len(sourceRelatedRecords),
		})
	} else {
		gf.Log().Info(gfctx, "没有新的收藏夹关联记录需要插入", gf.Map{
			"user_uuid":     userUUID,
			"skipped_count": skippedCount,
			"error_count":   errorCount,
		})
	}

	gf.Log().Info(gfctx, "保存收藏夹到用户收件箱来源关联表完成", gf.Map{
		"user_uuid":        userUUID,
		"success":          true,
		"inserted_records": len(sourceRelatedRecords),
		"skipped_records":  skippedCount,
		"error_records":    errorCount,
	})

	return nil
}

// VideoItem 表示从 API 返回的视频项目数据
type VideoItem struct {
	AwemeID          string  `json:"aweme_id"`
	CreateTime       *int64  `json:"create_time,omitempty"`
	Title            *string `json:"title,omitempty"`
	Desc             *string `json:"desc,omitempty"`
	AuthorNickname   *string `json:"author_nickname,omitempty"`
	AuthorUserID     *int64  `json:"author_user_id,omitempty"`
	Duration         *int64  `json:"duration,omitempty"`
	PlayCount        *int64  `json:"play_count,omitempty"`
	LikeCount        *int64  `json:"like_count,omitempty"`
	CommentCount     *int64  `json:"comment_count,omitempty"`
	ShareCount       *int64  `json:"share_count,omitempty"`
	CollectCount     *int64  `json:"collect_count,omitempty"`
	VideoCover       *string `json:"video_cover,omitempty"`
	VideoDownloadURL *string `json:"video_download_url,omitempty"`
}

// ToMap 将 VideoItem 转换为 map[string]interface{} 用于日志记录
func (v *VideoItem) ToMap() map[string]interface{} {
	result := map[string]interface{}{
		"aweme_id": v.AwemeID,
	}

	if v.CreateTime != nil {
		result["create_time"] = *v.CreateTime
	}
	if v.Title != nil {
		result["title"] = *v.Title
	}
	if v.Desc != nil {
		result["desc"] = *v.Desc
	}
	if v.AuthorNickname != nil {
		result["author_nickname"] = *v.AuthorNickname
	}
	if v.PlayCount != nil {
		result["play_count"] = *v.PlayCount
	}
	if v.LikeCount != nil {
		result["like_count"] = *v.LikeCount
	}
	if v.CommentCount != nil {
		result["comment_count"] = *v.CommentCount
	}
	if v.ShareCount != nil {
		result["share_count"] = *v.ShareCount
	}

	return result
}

// handleSyncCollectsVideos 同步指定收藏夹的视频数据
// 该函数调用 MediaCrawler 的 SyncCollectionWithCookies API 来同步收藏夹视频
// 并返回强类型的 VideoItem 列表
//
// 参数:
//   - collect: 收藏夹信息，必须包含有效的 CollectsID
//   - user: 用户信息，必须包含有效的 UUID 和 DouyinCookie
//
// 返回:
//   - []VideoItem: 同步成功的视频项目列表，如果失败则返回空列表
//
// 注意:
//   - 该函数会自动处理 API 响应中的 video_items 和 aweme_ids 字段
//   - 支持向后兼容，当没有 video_items 时会使用 aweme_ids
//   - 所有错误都会被记录到日志中，但不会导致 panic
func handleSyncCollectsVideos(collect entity.DouyinCollect, user entity.User) []VideoItem {
	var gfctx = gctx.New()

	// 参数验证
	if collect.CollectsID == 0 {
		gf.Log().Error(gfctx, "收藏夹ID无效", gf.Map{
			"user":       user.UUID,
			"collect_id": collect.CollectsID,
		})
		return []VideoItem{}
	}

	if user.UUID == "" {
		gf.Log().Error(gfctx, "用户UUID无效", gf.Map{
			"user":       user.UUID,
			"collect_id": collect.CollectsID,
		})
		return []VideoItem{}
	}

	if user.DouyinCookie == "" {
		gf.Log().Error(gfctx, "用户Cookie无效", gf.Map{
			"user":       user.UUID,
			"collect_id": collect.CollectsID,
		})
		return []VideoItem{}
	}

	// 预分配切片容量以提高性能
	videoItems := make([]VideoItem, 0, 100) // 预估每个收藏夹可能有100个视频

	// 记录开始时间用于性能监控
	startTime := time.Now()

	gf.Log().Info(gfctx, "开始同步收藏夹视频", gf.Map{
		"user":         user.UUID,
		"collect_id":   collect.CollectsID,
		"collect_name": collect.CollectsName,
	})

	// 创建 MediaCrawler 客户端
	apiClient, err := (&Douyin{}).createMediaCrawlerClient()
	if err != nil {
		gf.Log().Error(gfctx, "创建MediaCrawler客户端失败", gf.Map{
			"error":      err.Error(),
			"user":       user.UUID,
			"collect_id": collect.CollectsID,
			"duration":   time.Since(startTime).String(),
		})
		return videoItems
	}

	// 创建上下文，设置合理的超时时间
	// 同步操作可能较慢，特别是对于大型收藏夹
	ctx, cancel := context.WithTimeout(context.Background(), 120*time.Second)
	defer cancel()

	// 调用 SyncCollectionWithCookies API
	// 该接口会自动保存视频数据到数据库并建立关联关系
	gf.Log().Debug(gfctx, "调用SyncCollectionWithCookies API", gf.Map{
		"user":       user.UUID,
		"collect_id": collect.CollectsID,
		"api_url":    fmt.Sprintf("/api/v1/douyin/collection/sync/%s", gconv.String(collect.CollectsID)),
	})

	resp, httpResp, err := apiClient.APIAPI.SyncCollectionWithCookies(ctx, gconv.String(collect.CollectsID)).
		Cookies(user.DouyinCookie).
		Execute()

	// 详细的错误处理和日志记录
	if err != nil {
		// 检查是否是上下文超时错误
		if ctx.Err() == context.DeadlineExceeded {
			gf.Log().Error(gfctx, "同步收藏夹视频超时", gf.Map{
				"error":      err.Error(),
				"user":       user.UUID,
				"collect_id": collect.CollectsID,
				"timeout":    "120s",
				"duration":   time.Since(startTime).String(),
			})
		} else {
			gf.Log().Error(gfctx, "同步收藏夹视频API调用失败", gf.Map{
				"error":      err.Error(),
				"user":       user.UUID,
				"collect_id": collect.CollectsID,
				"duration":   time.Since(startTime).String(),
			})
		}
		return videoItems
	}

	// HTTP 响应状态检查
	if httpResp == nil {
		gf.Log().Error(gfctx, "HTTP响应为空", gf.Map{
			"user":       user.UUID,
			"collect_id": collect.CollectsID,
			"duration":   time.Since(startTime).String(),
		})
		return videoItems
	}

	if httpResp.StatusCode != 200 {
		gf.Log().Error(gfctx, "同步收藏夹视频HTTP状态异常", gf.Map{
			"status_code": httpResp.StatusCode,
			"status_text": httpResp.Status,
			"user":        user.UUID,
			"collect_id":  collect.CollectsID,
			"duration":    time.Since(startTime).String(),
		})
		return videoItems
	}

	// 响应数据验证
	if resp == nil {
		gf.Log().Error(gfctx, "API响应数据为空", gf.Map{
			"user":       user.UUID,
			"collect_id": collect.CollectsID,
			"duration":   time.Since(startTime).String(),
		})
		return videoItems
	}

	// 从强类型响应中获取 video_items 列表（包含 aweme_id 和 create_time）
	if resp.VideoItems != nil && len(resp.VideoItems) > 0 {
		gf.Log().Info(gfctx, "处理 video_items 数据", gf.Map{
			"user":              user.UUID,
			"collect_id":        collect.CollectsID,
			"video_items_count": len(resp.VideoItems),
		})

		for _, apiVideoItem := range resp.VideoItems {
			// 将 API 响应的 VideoItemResponse 转换为内部的 VideoItem
			// VideoItemResponse 只包含 aweme_id 和 create_time 两个字段
			videoItem := VideoItem{
				AwemeID: apiVideoItem.GetAwemeId(),
			}

			// 处理 create_time - VideoItemResponse.CreateTime 是 int64 类型，不是指针
			if createTime := apiVideoItem.GetCreateTime(); createTime > 0 {
				videoItem.CreateTime = &createTime
			}

			if videoItem.AwemeID != "" {
				videoItems = append(videoItems, videoItem)
			}
		}
	}

	// 如果没有 video_items，尝试从 aweme_ids 获取（向后兼容）
	if len(videoItems) == 0 && len(resp.AwemeIds) > 0 {
		gf.Log().Info(gfctx, "使用 aweme_ids 向后兼容模式", gf.Map{
			"user":        user.UUID,
			"collect_id":  collect.CollectsID,
			"aweme_count": len(resp.AwemeIds),
		})

		for _, awemeIdStr := range resp.AwemeIds {
			if awemeIdStr != "" {
				// 构建简单的 video_item
				videoItem := VideoItem{
					AwemeID: awemeIdStr,
					// 其他字段为 nil，表示没有相关信息
				}
				videoItems = append(videoItems, videoItem)
			}
		}
	}

	// 提取 aweme_ids 用于日志记录
	var awemeIds []string
	for _, videoItem := range videoItems {
		awemeIds = append(awemeIds, videoItem.AwemeID)
	}

	// 记录同步统计信息
	syncStats := map[string]interface{}{
		"user":                            user.UUID,
		"collect_id":                      collect.CollectsID,
		"collect_name":                    collect.CollectsName,
		"status_code":                     httpResp.StatusCode,
		"video_items_count":               len(videoItems),
		"aweme_count":                     len(awemeIds),
		"duration":                        time.Since(startTime).String(),
		"collections_synced":              resp.CollectionsSynced,
		"videos_synced":                   resp.VideosSynced,
		"relations_created":               resp.RelationsCreated,
		"relations_existing":              resp.RelationsExisting,
		"trendinsight_relations_created":  resp.TrendinsightRelationsCreated,
		"trendinsight_relations_existing": resp.TrendinsightRelationsExisting,
	}

	// 检查是否有错误信息
	if resp.Errors != nil && len(resp.Errors) > 0 {
		syncStats["api_errors"] = resp.Errors
		gf.Log().Warning(gfctx, "同步过程中API返回了错误信息", syncStats)
	}

	gf.Log().Info(gfctx, "收藏夹视频同步完成", syncStats)

	// 检查同步结果
	if len(videoItems) == 0 {
		gf.Log().Info(gfctx, "收藏夹同步完成，但没有获取到视频数据", gf.Map{
			"user":              user.UUID,
			"collect_id":        collect.CollectsID,
			"collect_name":      collect.CollectsName,
			"videos_synced":     resp.VideosSynced,
			"relations_created": resp.RelationsCreated,
			"possible_reasons":  []string{"收藏夹为空", "所有视频已存在", "Cookie权限不足", "收藏夹不可访问"},
			"duration":          time.Since(startTime).String(),
		})
		return videoItems
	}

	// 性能优化：只在需要时记录详细的视频项目信息
	// 这里简化处理，可以根据需要添加更复杂的日志级别判断
	if len(videoItems) <= 10 { // 只有当视频数量较少时才记录详细信息
		// 转换为 map 用于详细日志记录
		videoItemMaps := make([]map[string]interface{}, 0, len(videoItems))
		for _, item := range videoItems {
			videoItemMaps = append(videoItemMaps, item.ToMap())
		}

		gf.Log().Debug(gfctx, "返回详细的video_items数据", gf.Map{
			"user":              user.UUID,
			"collect_id":        collect.CollectsID,
			"video_items_count": len(videoItems),
			"aweme_ids":         awemeIds,
			"video_items":       videoItemMaps,
			"duration":          time.Since(startTime).String(),
		})
	}

	gf.Log().Info(gfctx, "成功返回video_items数据", gf.Map{
		"user":              user.UUID,
		"collect_id":        collect.CollectsID,
		"collect_name":      collect.CollectsName,
		"video_items_count": len(videoItems),
		"duration":          time.Since(startTime).String(),
	})

	return videoItems
}

func generateDouyinUrlById(videoId string) string {
	return fmt.Sprintf("https://www.douyin.com/video/%s", videoId)
}

// GetSyncRecords 获取用户的抖音同步记录列表
func (api *Douyin) GetSyncRecords(c *gf.GinCtx) {
	var gfctx = gctx.New()

	// 获取用户信息
	getuser, exists := c.Get("user")
	if !exists {
		gf.Failed().SetMsg("用户信息获取失败").Regin(c)
		return
	}
	userOBJ := getuser.(gf.UserObj)
	userUUID := string(userOBJ.UserUUID)

	// 获取请求参数
	param, _ := gf.RequestParam(c)

	// 解析分页参数
	page := gconv.Int(param["page"])
	if page <= 0 {
		page = 1
	}

	pageSize := gconv.Int(param["page_size"])
	if pageSize <= 0 {
		pageSize = 20
	}
	if pageSize > 100 {
		pageSize = 100 // 限制最大页面大小
	}

	// 创建同步记录服务
	syncRecordService := services.NewDouyinCollectSyncRecordService()

	// 获取用户同步记录
	records, total, err := syncRecordService.GetUserSyncRecords(userUUID, page, pageSize)
	if err != nil {
		gf.Log().Error(gfctx, "获取用户同步记录失败", gf.Map{
			"error":     err.Error(),
			"user_uuid": userUUID,
			"page":      page,
			"page_size": pageSize,
		})
		gf.Failed().SetMsg("获取同步记录失败: " + err.Error()).Regin(c)
		return
	}

	// 构建响应数据
	responseData := gf.Map{
		"records": records,
		"pagination": gf.Map{
			"page":       page,
			"page_size":  pageSize,
			"total":      total,
			"total_page": (total + int64(pageSize) - 1) / int64(pageSize),
		},
	}

	gf.Log().Info(gfctx, "获取用户同步记录成功", gf.Map{
		"user_uuid":     userUUID,
		"page":          page,
		"page_size":     pageSize,
		"total":         total,
		"records_count": len(records),
	})

	gf.Success().SetData(responseData).SetMsg("获取同步记录成功").Regin(c)
}

// GetSyncStats 获取用户的抖音同步统计信息
func (api *Douyin) GetSyncStats(c *gf.GinCtx) {
	var gfctx = gctx.New()

	// 获取用户信息
	getuser, exists := c.Get("user")
	if !exists {
		gf.Failed().SetMsg("用户信息获取失败").Regin(c)
		return
	}
	userOBJ := getuser.(gf.UserObj)
	userUUID := string(userOBJ.UserUUID)

	// 获取请求参数
	param, _ := gf.RequestParam(c)

	// 解析统计天数参数
	days := gconv.Int(param["days"])
	if days <= 0 {
		days = 30 // 默认统计最近30天
	}
	if days > 365 {
		days = 365 // 限制最大统计天数为1年
	}

	// 创建用户收件箱视频关联服务
	videoRelatedService := services.NewUserInboxVideoRelatedService()

	// 获取用户视频统计信息
	gf.Log().Info(gfctx, "开始调用GetUserVideoStats", gf.Map{
		"user_uuid": userUUID,
		"days":      days,
	})

	stats, err := videoRelatedService.GetUserVideoStats(userUUID, days)
	if err != nil {
		gf.Log().Error(gfctx, "获取用户视频统计失败", gf.Map{
			"error":     err.Error(),
			"user_uuid": userUUID,
			"days":      days,
		})
		gf.Failed().SetMsg("查询统计信息失败").Regin(c)
		return
	}

	gf.Log().Info(gfctx, "获取用户视频统计成功", gf.Map{
		"user_uuid":     userUUID,
		"days":          days,
		"total_records": stats.TotalRecords,
		"source_stats":  stats.SourceStats,
	})

	gf.Success().SetData(stats).SetMsg("获取视频统计成功").Regin(c)
}

// GetSyncRecordRelated 获取指定同步记录的关联视频列表
func (api *Douyin) GetSyncRecordRelated(c *gf.GinCtx) {
	var gfctx = gctx.New()

	// 获取用户信息
	getuser, exists := c.Get("user")
	if !exists {
		gf.Failed().SetMsg("用户信息获取失败").Regin(c)
		return
	}
	userOBJ := getuser.(gf.UserObj)
	userUUID := string(userOBJ.UserUUID)

	// 获取请求参数
	param, _ := gf.RequestParam(c)

	// 获取同步记录UUID参数
	syncRecordUUID := gconv.String(param["sync_record_uuid"])
	if syncRecordUUID == "" {
		gf.Failed().SetMsg("同步记录UUID不能为空").Regin(c)
		return
	}

	// 解析分页参数
	page := gconv.Int(param["page"])
	if page <= 0 {
		page = 1
	}

	pageSize := gconv.Int(param["page_size"])
	if pageSize <= 0 {
		pageSize = 20
	}
	if pageSize > 100 {
		pageSize = 100 // 限制最大页面大小
	}

	// 创建同步记录服务
	syncRecordService := services.NewDouyinCollectSyncRecordService()

	// 获取包含视频详细信息的数据
	records, total, err := syncRecordService.GetSyncRecordRelatedWithVideo(userUUID, syncRecordUUID, page, pageSize)
	if err != nil {
		gf.Log().Error(gfctx, "获取同步记录关联失败", gf.Map{
			"error":            err.Error(),
			"user_uuid":        userUUID,
			"sync_record_uuid": syncRecordUUID,
			"page":             page,
			"page_size":        pageSize,
		})
		gf.Failed().SetMsg("获取同步记录关联失败: " + err.Error()).Regin(c)
		return
	}

	// 构建响应数据
	responseData := gf.Map{
		"records": records,
		"pagination": gf.Map{
			"page":       page,
			"page_size":  pageSize,
			"total":      total,
			"total_page": (total + int64(pageSize) - 1) / int64(pageSize),
		},
		"sync_record_uuid": syncRecordUUID,
	}

	gf.Log().Info(gfctx, "获取同步记录关联成功", gf.Map{
		"user_uuid":        userUUID,
		"sync_record_uuid": syncRecordUUID,
		"page":             page,
		"page_size":        pageSize,
		"total":            total,
		"records_count":    len(records),
	})

	gf.Success().SetData(responseData).SetMsg("获取同步记录关联成功").Regin(c)
}

// GetSyncRecordRelatedList 获取用户所有同步记录的关联视频列表
func (api *Douyin) GetSyncRecordRelatedList(c *gf.GinCtx) {
	var gfctx = gctx.New()

	// 获取用户信息
	getuser, exists := c.Get("user")
	if !exists {
		gf.Failed().SetMsg("用户信息获取失败").Regin(c)
		return
	}
	userOBJ := getuser.(gf.UserObj)
	userUUID := string(userOBJ.UserUUID)

	// 获取请求参数
	param, _ := gf.RequestParam(c)

	// 解析分页参数
	page := gconv.Int(param["page"])
	if page <= 0 {
		page = 1
	}

	pageSize := gconv.Int(param["page_size"])
	if pageSize <= 0 {
		pageSize = 20
	}
	if pageSize > 100 {
		pageSize = 100 // 限制最大页面大小
	}

	// 创建同步记录服务
	syncRecordService := services.NewDouyinCollectSyncRecordService()

	// 获取包含视频详细信息的数据
	records, total, err := syncRecordService.GetUserSyncRecordRelatedWithVideo(userUUID, page, pageSize)
	if err != nil {
		gf.Log().Error(gfctx, "获取用户同步记录关联失败", gf.Map{
			"error":     err.Error(),
			"user_uuid": userUUID,
			"page":      page,
			"page_size": pageSize,
		})
		gf.Failed().SetMsg("获取同步记录关联失败: " + err.Error()).Regin(c)
		return
	}

	// 构建响应数据
	responseData := gf.Map{
		"records": records,
		"pagination": gf.Map{
			"page":       page,
			"page_size":  pageSize,
			"total":      total,
			"total_page": (total + int64(pageSize) - 1) / int64(pageSize),
		},
	}

	gf.Log().Info(gfctx, "获取用户同步记录关联成功", gf.Map{
		"user_uuid":     userUUID,
		"page":          page,
		"page_size":     pageSize,
		"total":         total,
		"records_count": len(records),
	})

	gf.Success().SetData(responseData).SetMsg("获取同步记录关联成功").Regin(c)
}

// GetVideoDetail 获取抖音视频详情
// GetVideoDetail 获取抖音视频详情
func (api *Douyin) GetVideoDetail(c *gf.GinCtx) {
	var gfctx = gctx.New()

	// 获取路径参数中的aweme_id
	awemeID := c.Param("aweme_id")
	if awemeID == "" {
		// 如果路径参数为空，尝试从请求参数中获取
		param, _ := gf.RequestParam(c)
		awemeID = gconv.String(param["aweme_id"])

		if awemeID == "" {
			gf.Failed().SetMsg("aweme_id参数不能为空").Regin(c)
			return
		}
	}

	// 验证aweme_id格式
	if len(awemeID) < 10 {
		gf.Failed().SetMsg("aweme_id格式不正确").Regin(c)
		return
	}

	// 创建 MediaCrawler 客户端
	apiClient, err := api.createMediaCrawlerClient()
	if err != nil {
		gf.Log().Error(gfctx, "创建MediaCrawler客户端失败", gf.Map{
			"error":    err.Error(),
			"aweme_id": awemeID,
		})
		gf.Failed().SetMsg("服务暂时不可用，请稍后重试").Regin(c)
		return
	}

	// 创建上下文
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// 调用 MediaCrawler 的视频详情API
	resp, httpResp, err := apiClient.APIAPI.GetVideoRpcAutoCookies(ctx, awemeID).Execute()
	if err != nil {
		gf.Log().Error(gfctx, "获取视频详情失败", gf.Map{
			"error":    err.Error(),
			"aweme_id": awemeID,
		})

		// 处理特定的MediaCrawler错误
		errorMsg := err.Error()
		if strings.Contains(errorMsg, "NoneType") && strings.Contains(errorMsg, "account") {
			gf.Failed().SetMsg("视频信息获取失败，可能是视频不存在或已被删除").Regin(c)
		} else if strings.Contains(errorMsg, "404") {
			gf.Failed().SetMsg("视频不存在").Regin(c)
		} else if strings.Contains(errorMsg, "403") {
			gf.Failed().SetMsg("访问被拒绝，可能需要更新Cookie").Regin(c)
		} else if strings.Contains(errorMsg, "timeout") {
			gf.Failed().SetMsg("请求超时，请稍后重试").Regin(c)
		} else {
			gf.Failed().SetMsg("获取视频详情失败，请稍后重试").Regin(c)
		}
		return
	}

	// 检查HTTP状态码
	if httpResp != nil && httpResp.StatusCode != 200 {
		gf.Log().Error(gfctx, "获取视频详情HTTP状态异常", gf.Map{
			"status_code": httpResp.StatusCode,
			"aweme_id":    awemeID,
		})

		switch httpResp.StatusCode {
		case 404:
			gf.Failed().SetMsg("视频不存在").Regin(c)
		case 403:
			gf.Failed().SetMsg("访问被拒绝，可能需要更新Cookie").Regin(c)
		case 429:
			gf.Failed().SetMsg("请求过于频繁，请稍后重试").Regin(c)
		case 500:
			gf.Failed().SetMsg("服务器内部错误，请稍后重试").Regin(c)
		default:
			gf.Failed().SetMsg(fmt.Sprintf("请求失败，状态码: %d", httpResp.StatusCode)).Regin(c)
		}
		return
	}

	// 检查响应数据
	if resp == nil {
		gf.Log().Error(gfctx, "视频详情响应数据为空", gf.Map{
			"aweme_id": awemeID,
		})
		gf.Failed().SetMsg("视频详情数据为空").Regin(c)
		return
	}

	gf.Log().Info(gfctx, "获取视频详情成功", gf.Map{
		"aweme_id":    awemeID,
		"status_code": httpResp.StatusCode,
		"resp_type":   fmt.Sprintf("%T", resp),
	})

	// 返回成功响应
	gf.Success().SetData(resp).SetMsg("获取视频详情成功").Regin(c)
}

func handleAssetsCreate(videoList []*entity.DouyinVideoInfo, collect entity.DouyinCollect, user entity.User) []*entity.Assets {
	var usedList []*entity.Assets
	for _, video := range videoList {
		// 使用 mapper 创建基础数据
		assetData := assets_mapper.FromDouyinVideoInfo(video)

		// 创建完整的 asset 对象，包含额外的字段
		asset := &entity.Assets{
			BaseEntity: entity.BaseEntity{
				UUID: gf.GenerateUUID(),
			},
			UserUUID:         user.UUID,
			Source:           cons.Collection.Value,
			AnalysisStatus:   cons.Preprocessing.Value,
			GenerationStatus: cons.Pending.Value,
			Insight:          collect.CollectsName,
		}

		// 使用 mergo 合并 mapper 生成的数据
		if err := mergo.Merge(asset, assetData, mergo.WithOverride); err != nil {
			gf.Log().Error(gctx.New(), "合并asset数据失败", gf.Map{
				"error":    err.Error(),
				"aweme_id": video.AwemeID,
			})
			continue // 跳过这个视频，继续处理下一个
		}

		usedList = append(usedList, asset)
	}
	if len(usedList) == 0 {
		return usedList
	}

	gf.Model(*tableAssets).Data(usedList).Save()
	return usedList
}

func HandleSingleUserSyncCollection(user entity.User) (int, []string, []string) {
	gf.Log().Print(gctx.New(), "HandleSingleUserSyncCollection")
	var gfctx = gctx.New()
	var allAwemeIds []string           // 收集所有的 aweme_id
	var newAwemeIds []string           // 收集新创建的 aweme_id
	var allVideoRelatedErrors []string // 收集视频关联创建错误
	var totalVideoRelatedCount int     // 总的视频关联记录数

	// 初始化变量和 VideoRelatedService
	videoRelatedService := services.NewUserInboxVideoRelatedService()

	// 调用 handleSyncCollects 获取用户收藏夹列表
	var collectList = handleSyncCollects(user)

	// 遍历每个收藏夹
	for _, collect := range collectList {
		// 调用 handleSyncCollectsVideos 获取视频列表
		var videoList = handleSyncCollectsVideos(collect, user)

		// 从视频列表中提取 video_items (aweme_id, create_time)
		var collectAwemeIds []string
		var videoItemsWithTime []VideoItem
		var videoItemsForService []services.VideoItemWithTime
		for _, video := range videoList {
			// video 现在是 VideoItem 类型
			if video.AwemeID != "" {
				allAwemeIds = append(allAwemeIds, video.AwemeID)
				collectAwemeIds = append(collectAwemeIds, video.AwemeID)

				// 直接使用强类型的 video_item
				videoItemsWithTime = append(videoItemsWithTime, video)

				// 转换为服务层需要的格式
				serviceItem := services.VideoItemWithTime{
					AwemeID:    video.AwemeID,
					CreateTime: video.CreateTime,
				}
				videoItemsForService = append(videoItemsForService, serviceItem)
			}
		}

		// 转换为 map 用于日志记录
		var videoItemMaps []map[string]interface{}
		for _, item := range videoItemsWithTime {
			videoItemMaps = append(videoItemMaps, item.ToMap())
		}

		// 记录收集到的视频ID详情
		gf.Log().Info(gfctx, "收藏夹视频ID收集完成", gf.Map{
			"user_uuid":                     user.UUID,
			"collect_id":                    collect.CollectsID,
			"collect_name":                  collect.CollectsName,
			"video_list_count":              len(videoList),
			"collect_aweme_ids":             collectAwemeIds,
			"aweme_count":                   len(collectAwemeIds),
			"video_items_with_time":         videoItemMaps,
			"video_items_for_service_count": len(videoItemsForService),
		})

		// 判断是否存在有效的 video_items
		if len(collectAwemeIds) > 0 {
			// 使用收藏夹ID作为SourceId
			sourceId := gconv.String(collect.CollectsID)

			gf.Log().Info(gfctx, "开始处理收藏夹视频关联记录", gf.Map{
				"user_uuid":   user.UUID,
				"collect_id":  collect.CollectsID,
				"source_id":   sourceId,
				"aweme_ids":   collectAwemeIds,
				"aweme_count": len(collectAwemeIds),
			})

			// 调用 GetNewAwemeIds 检查哪些是新视频
			newIds, err := videoRelatedService.GetNewAwemeIds(user.UUID, collectAwemeIds)
			if err != nil {
				// 失败：记录错误信息
				errorMsg := fmt.Sprintf("收藏夹 %s 获取新视频ID列表失败: %v", sourceId, err)
				allVideoRelatedErrors = append(allVideoRelatedErrors, errorMsg)
				gf.Log().Error(gfctx, "获取新视频ID列表失败", gf.Map{
					"user_uuid":   user.UUID,
					"collect_id":  collect.CollectsID,
					"source_id":   sourceId,
					"aweme_ids":   collectAwemeIds,
					"aweme_count": len(collectAwemeIds),
					"error":       err.Error(),
				})
			} else {
				// 成功：记录新视频ID
				newAwemeIds = append(newAwemeIds, newIds...)
				gf.Log().Info(gfctx, "获取新视频ID列表成功", gf.Map{
					"user_uuid":  user.UUID,
					"collect_id": collect.CollectsID,
					"source_id":  sourceId,
					"new_ids":    newIds,
					"new_count":  len(newIds),
				})
			}

			// 调用 BatchCreateFromCollectSyncWithTime 批量创建关联记录（携带创建时间）
			err = videoRelatedService.BatchCreateFromCollectSyncWithTime(user.UUID, sourceId, videoItemsForService)
			if err != nil {
				// 失败：记录错误信息
				errorMsg := fmt.Sprintf("收藏夹 %s 创建视频关联记录失败: %v", sourceId, err)
				allVideoRelatedErrors = append(allVideoRelatedErrors, errorMsg)
				gf.Log().Error(gfctx, "创建用户收件箱视频关联记录失败", gf.Map{
					"user_uuid":   user.UUID,
					"collect_id":  collect.CollectsID,
					"source_id":   sourceId,
					"aweme_ids":   collectAwemeIds,
					"aweme_count": len(collectAwemeIds),
					"error":       err.Error(),
				})
			} else {
				// 成功：增加总关联数并记录日志
				totalVideoRelatedCount += len(collectAwemeIds)
				gf.Log().Info(gfctx, "成功创建用户收件箱视频关联记录", gf.Map{
					"user_uuid":   user.UUID,
					"collect_id":  collect.CollectsID,
					"source_id":   sourceId,
					"aweme_count": len(collectAwemeIds),
				})
			}
		} else {
			// 否：记录警告: 收藏夹无视频
			gf.Log().Warning(gfctx, "收藏夹中没有有效的视频ID", gf.Map{
				"user_uuid":        user.UUID,
				"collect_id":       collect.CollectsID,
				"collect_name":     collect.CollectsName,
				"video_list_count": len(videoList),
			})
		}
		// 结束当前收藏夹处理，继续下一个收藏夹
	}

	// 记录总体统计信息
	gf.Log().Info(gfctx, "收藏夹同步完成统计", gf.Map{
		"user_uuid":            user.UUID,
		"total_aweme_ids":      len(allAwemeIds),
		"new_aweme_ids":        len(newAwemeIds),
		"total_video_related":  totalVideoRelatedCount,
		"video_related_errors": len(allVideoRelatedErrors),
	})

	// 如果有视频关联创建错误，记录详细信息
	if len(allVideoRelatedErrors) > 0 {
		gf.Log().Warning(gfctx, "部分视频关联记录创建失败", gf.Map{
			"user_uuid": user.UUID,
			"errors":    allVideoRelatedErrors,
		})
	}

	// 返回总关联数、新视频ID列表和错误列表
	return totalVideoRelatedCount, newAwemeIds, allVideoRelatedErrors
}

func (api *Douyin) SyncCollects(c *gf.GinCtx) {
	getuser, _ := c.Get("user")
	userOBJ := getuser.(gf.UserObj)
	userUUID := string(userOBJ.UserUUID)

	var (
		user     = entity.User{}
		userRule = validators.UserValidator{}
	)
	err := gf.Model(*tableUser).Scan(&user, *fieldUUID, userUUID)
	if err != nil {
		gf.Failed().SetMsg("用户获取失败").Regin(c)
		return
	}

	if err1 := gf.Validator().Assoc(user).Data(userRule).Locale("zh-CN").Run(c); err1 != nil {
		errKey, _ := err1.FirstItem()
		var code = cons.UserInfoCommercialPosNotComplete.Value
		if errKey == "DouyinCookie" {
			code = cons.UserInfoDouyinCookieNotComplete.Value
		}
		gf.Failed().SetMsg(err1.String()).SetCode(code).Regin(c)
		return
	}

	// 记录同步开始时间
	syncStartTime := time.Now()

	// 创建同步记录服务实例
	syncRecordService := services.NewDouyinCollectSyncRecordService()

	// 执行同步操作，使用 defer 确保异常情况下也能记录
	var count int
	var newAwemeIds []string
	var errors []string
	var syncStatus string = cons.Success.Value

	// 使用 defer 函数确保在任何情况下都能记录同步结果
	defer func() {
		if r := recover(); r != nil {
			// 捕获 panic，记录为失败
			syncStatus = cons.Failed.Value
			count = 0
			gf.Log().Error(gctx.New(), "抖音同步过程中发生异常", gf.Map{
				"user_uuid": userUUID,
				"panic":     r,
				"duration":  time.Since(syncStartTime).String(),
			})
		}

		// 根据同步结果创建记录
		if count > 0 {
			// 同步成功，创建记录，但只记录新创建的视频
			_, err := syncRecordService.CreateSyncRecordWithAwemeIds(userUUID, len(newAwemeIds), syncStatus, newAwemeIds)
			if err != nil {
				// 记录创建失败，记录日志但不影响主流程
				gf.Log().Error(gctx.New(), "创建抖音同步记录失败", gf.Map{
					"user_uuid":           userUUID,
					"sync_count":          len(newAwemeIds),
					"sync_status":         syncStatus,
					"new_aweme_count":     len(newAwemeIds),
					"total_related_count": count,
					"error_count":         len(errors),
					"error":               err.Error(),
				})
			} else {
				gf.Log().Info(gctx.New(), "抖音同步记录创建成功", gf.Map{
					"user_uuid":           userUUID,
					"sync_count":          len(newAwemeIds),
					"sync_status":         syncStatus,
					"new_aweme_count":     len(newAwemeIds),
					"total_related_count": count,
					"error_count":         len(errors),
					"duration":            time.Since(syncStartTime).String(),
				})
			}
		} else {
			// 同步数量为0，不创建记录，但记录日志
			logLevel := "Info"
			logMsg := "抖音同步完成，无新增视频"
			if syncStatus == cons.Failed.Value {
				logLevel = "Error"
				logMsg = "抖音同步失败"
			}

			if logLevel == "Error" {
				gf.Log().Error(gctx.New(), logMsg, gf.Map{
					"user_uuid": userUUID,
					"duration":  time.Since(syncStartTime).String(),
				})
			} else {
				gf.Log().Info(gctx.New(), logMsg, gf.Map{
					"user_uuid": userUUID,
					"duration":  time.Since(syncStartTime).String(),
				})
			}
		}
	}()

	// 执行同步操作
	count, newAwemeIds, errors = HandleSingleUserSyncCollection(user)

	// 如果有错误，记录详细信息
	if len(errors) > 0 {
		gf.Log().Warning(gctx.New(), "同步过程中出现部分错误", gf.Map{
			"user_uuid":   userUUID,
			"error_count": len(errors),
			"errors":      errors,
		})
	}

	// 检查同步结果并返回响应
	if syncStatus == cons.Failed.Value {
		gf.Failed().SetMsg("同步过程中发生错误，请稍后重试").Regin(c)
		return
	}

	if count > 0 {
		if len(errors) > 0 {
			gf.Success().SetMsg(fmt.Sprintf("同步完成，创建 %d 个关联记录，其中新视频 %d 个，但有 %d 个错误", count, len(newAwemeIds), len(errors))).Regin(c)
		} else {
			gf.Success().SetMsg(fmt.Sprintf("同步成功，创建 %d 个关联记录，其中新视频 %d 个", count, len(newAwemeIds))).Regin(c)
		}
	} else {
		if len(errors) > 0 {
			gf.Failed().SetMsg(fmt.Sprintf("同步失败，出现 %d 个错误", len(errors))).Regin(c)
		} else {
			gf.Failed().SetMsg("本次没有同步到符合条件的视频").Regin(c)
		}
	}
}

// GetVideoRelatedList 获取用户收件箱视频关联记录列表
func (api *Douyin) GetVideoRelatedList(c *gf.GinCtx) {
	var gfctx = gctx.New()

	// 获取当前用户信息
	getuser, exists := c.Get("user")
	if !exists {
		gf.Failed().SetMsg("用户信息获取失败").Regin(c)
		return
	}
	userOBJ := getuser.(gf.UserObj)
	userUUID := string(userOBJ.UserUUID)

	// 获取用户详细信息
	var user entity.User
	err := gf.Model("user").Scan(&user, "uuid", userUUID)
	if err != nil {
		gf.Log().Error(gfctx, "获取用户详细信息失败", gf.Map{"error": err.Error(), "user_uuid": userUUID})
		gf.Failed().SetMsg("用户信息获取失败").Regin(c)
		return
	}

	// 获取查询参数
	sourceType := c.Query("source_type")
	sortBy := c.DefaultQuery("sort_by", "create_time")
	sortOrder := c.DefaultQuery("sort_order", "desc")
	pageStr := c.DefaultQuery("page", "1")
	pageSizeStr := c.DefaultQuery("page_size", "20")

	// 参数验证
	page, err := strconv.Atoi(pageStr)
	if err != nil || page < 1 {
		gf.Failed().SetMsg("页码参数无效").Regin(c)
		return
	}

	pageSize, err := strconv.Atoi(pageSizeStr)
	if err != nil || pageSize < 1 || pageSize > 100 {
		gf.Failed().SetMsg("每页数量参数无效，范围：1-100").Regin(c)
		return
	}

	// 验证 source_type 参数
	if sourceType != "" && sourceType != string(entity.SourceTypeKeyword) && sourceType != string(entity.SourceTypeAuthor) && sourceType != string(entity.SourceTypeCollect) {
		gf.Failed().SetMsg("来源类型参数无效，支持：KEYWORD、AUTHOR、COLLECT").Regin(c)
		return
	}

	// 验证 sort_by 参数
	if sortBy != "create_time" && sortBy != "publish_time" {
		gf.Failed().SetMsg("排序字段参数无效，支持：create_time、publish_time").Regin(c)
		return
	}

	// 验证 sort_order 参数
	if sortOrder != "asc" && sortOrder != "desc" {
		gf.Failed().SetMsg("排序方向参数无效，支持：asc、desc").Regin(c)
		return
	}

	// 创建服务实例
	videoRelatedService := services.NewUserInboxVideoRelatedService()

	// 查询数据（包含关联的抖音视频信息）
	records, total, err := videoRelatedService.GetUserVideosWithFiltersAndAweme(
		user.UUID, sourceType, sortBy, sortOrder, page, pageSize)
	if err != nil {
		gf.Log().Error(gfctx, "查询视频关联记录失败", gf.Map{
			"user_uuid":   user.UUID,
			"source_type": sourceType,
			"sort_by":     sortBy,
			"sort_order":  sortOrder,
			"page":        page,
			"page_size":   pageSize,
			"error":       err.Error(),
		})
		gf.Failed().SetMsg("查询失败").Regin(c)
		return
	}

	// 构建返回数据
	responseData := map[string]interface{}{
		"list":      records,
		"total":     total,
		"page":      page,
		"page_size": pageSize,
	}

	// 记录查询日志
	gf.Log().Info(gfctx, "查询视频关联记录成功", gf.Map{
		"user_uuid":    user.UUID,
		"source_type":  sourceType,
		"sort_by":      sortBy,
		"sort_order":   sortOrder,
		"page":         page,
		"page_size":    pageSize,
		"total":        total,
		"result_count": len(records),
	})

	gf.Success().SetData(responseData).SetMsg("查询成功").Regin(c)
}

// GetVideoRelatedStats 获取用户收件箱视频关联记录统计信息
func (api *Douyin) GetVideoRelatedStats(c *gf.GinCtx) {
	var gfctx = gctx.New()

	// 获取当前用户信息
	getuser, exists := c.Get("user")
	if !exists {
		gf.Failed().SetMsg("用户信息获取失败").Regin(c)
		return
	}
	userOBJ := getuser.(gf.UserObj)
	userUUID := string(userOBJ.UserUUID)

	// 获取用户详细信息
	var user entity.User
	err := gf.Model("user").Scan(&user, "uuid", userUUID)
	if err != nil {
		gf.Log().Error(gfctx, "获取用户详细信息失败", gf.Map{"error": err.Error(), "user_uuid": userUUID})
		gf.Failed().SetMsg("用户信息获取失败").Regin(c)
		return
	}

	// 创建服务实例
	videoRelatedService := services.NewUserInboxVideoRelatedService()

	// 查询统计数据
	stats, err := videoRelatedService.GetDetailedStatsByUser(user.UUID)
	if err != nil {
		gf.Log().Error(gfctx, "查询视频关联记录统计失败", gf.Map{
			"user_uuid": user.UUID,
			"error":     err.Error(),
		})
		gf.Failed().SetMsg("查询统计信息失败").Regin(c)
		return
	}

	// 记录查询日志
	gf.Log().Info(gfctx, "查询视频关联记录统计成功", gf.Map{
		"user_uuid":       user.UUID,
		"total":           stats["total"],
		"today_new_count": stats["today_new_count"],
	})

	gf.Success().SetData(stats).SetMsg("查询成功").Regin(c)
}

// AddVideoToAssets 将 user_inbox_video_related 表中状态为 PENDING 的视频数据添加到 assets 表中
// @Summary 添加视频到素材库
// @Description 将 user_inbox_video_related 表中状态为 PENDING 的视频数据添加到 assets 表中，支持从收藏夹、关键词、作者等来源添加视频素材
// @Tags 抖音数据同步
// @Accept json
// @Produce json
// @Param request body object true "添加视频到素材库请求"
// @Param request.record_uuid body string true "视频关联记录UUID" minlength(32) maxlength(32) example("1234567890abcdef1234567890abcdef")
// @Success 200 {object} object "添加成功"
// @Success 200 {object} object{code=int,msg=string,data=object{asset_uuid=string,video_id=string,video_title=string,author=string,source=string,record_status=string}} "添加成功响应示例"
// @Failure 400 {object} object{code=int,msg=string,data=object{}} "请求参数错误"
// @Failure 400 {object} object{code=int,msg=string,data=object{}} "记录UUID参数不能为空"
// @Failure 400 {object} object{code=int,msg=string,data=object{}} "视频记录不存在或无权限访问"
// @Failure 400 {object} object{code=int,msg=string,data=object{}} "视频记录状态不是PENDING，只能处理状态为PENDING的记录"
// @Failure 400 {object} object{code=int,msg=string,data=object{}} "视频详细信息不存在，请先同步视频数据"
// @Failure 400 {object} object{code=int,msg=string,data=object{}} "该视频已经添加到素材库中"
// @Failure 401 {object} object{code=int,msg=string,data=object{}} "用户未登录"
// @Failure 500 {object} object{code=int,msg=string,data=object{}} "服务器内部错误"
// @Security BearerAuth
// @Router /client/user/douyin/addVideoToAssets [post]
func (api *Douyin) AddVideoToAssets(c *gf.GinCtx) {
	var gfctx = gctx.New()

	// 获取当前用户信息
	getuser, exists := c.Get("user")
	if !exists {
		gf.Failed().SetMsg("用户信息获取失败").Regin(c)
		return
	}
	userOBJ := getuser.(gf.UserObj)
	userUUID := string(userOBJ.UserUUID)

	// 获取请求参数
	param, _ := gf.RequestParam(c)
	recordUUID := gconv.String(param["record_uuid"])
	if recordUUID == "" {
		gf.Failed().SetMsg("记录UUID参数不能为空").Regin(c)
		return
	}

	gf.Log().Info(gfctx, "开始处理视频添加到素材库请求", gf.Map{
		"user_uuid":   userUUID,
		"record_uuid": recordUUID,
	})

	// 开始事务处理
	tx, err := gf.DB().Begin(gfctx)
	if err != nil {
		gf.Log().Error(gfctx, "开始事务失败", gf.Map{
			"error":       err.Error(),
			"user_uuid":   userUUID,
			"record_uuid": recordUUID,
		})
		gf.Failed().SetMsg("系统错误，请稍后重试").Regin(c)
		return
	}
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			gf.Log().Error(gfctx, "添加视频到素材库过程中发生异常", gf.Map{
				"user_uuid":   userUUID,
				"record_uuid": recordUUID,
				"panic":       r,
			})
			gf.Failed().SetMsg("处理过程中发生异常，请稍后重试").Regin(c)
		}
	}()

	// 1. 查询 user_inbox_video_related 表中的记录
	var videoRelated entity.UserInboxVideoRelated
	err = gf.Model("user_inbox_video_related").
		Where("uuid", recordUUID).
		Where("user_uuid", userUUID).
		Where("is_deleted", 0).
		Scan(&videoRelated)

	if err != nil {
		tx.Rollback()
		gf.Log().Error(gfctx, "查询视频关联记录失败", gf.Map{
			"error":       err.Error(),
			"user_uuid":   userUUID,
			"record_uuid": recordUUID,
		})
		gf.Failed().SetMsg("查询视频记录失败").Regin(c)
		return
	}

	if videoRelated.UUID == "" {
		tx.Rollback()
		gf.Failed().SetMsg("视频记录不存在或无权限访问").Regin(c)
		return
	}

	// 2. 验证记录的 handle_status 是否为 'PENDING'
	if videoRelated.HandleStatus != cons.Pending.Value {
		tx.Rollback()
		gf.Log().Warning(gfctx, "视频记录状态不是PENDING", gf.Map{
			"user_uuid":     userUUID,
			"record_uuid":   videoRelated.UUID,
			"handle_status": videoRelated.HandleStatus,
		})
		gf.Failed().SetMsg(fmt.Sprintf("视频记录状态为 %s，只能处理状态为 %s 的记录", videoRelated.HandleStatus, cons.Pending.Value)).Regin(c)
		return
	}

	gf.Log().Info(gfctx, "视频关联记录查询成功", gf.Map{
		"user_uuid":     userUUID,
		"record_uuid":   videoRelated.UUID,
		"aweme_id":      videoRelated.AwemeId,
		"source_type":   videoRelated.SourceType,
		"handle_status": videoRelated.HandleStatus,
	})

	// 3. 从 media_crawler 数据库查询对应的抖音视频信息
	var douyinAweme entity.DouyinAweme
	err = setting.CrawlerModel(&douyinAweme).
		Where("aweme_id", videoRelated.AwemeId).
		Scan(&douyinAweme)

	if err != nil {
		tx.Rollback()
		gf.Log().Error(gfctx, "查询抖音视频信息失败", gf.Map{
			"error":       err.Error(),
			"user_uuid":   userUUID,
			"record_uuid": videoRelated.UUID,
			"aweme_id":    videoRelated.AwemeId,
		})
		gf.Failed().SetMsg("查询视频详细信息失败").Regin(c)
		return
	}

	if douyinAweme.AwemeID == "" {
		tx.Rollback()
		gf.Log().Warning(gfctx, "抖音视频信息不存在", gf.Map{
			"user_uuid":   userUUID,
			"record_uuid": videoRelated.UUID,
			"aweme_id":    videoRelated.AwemeId,
		})
		gf.Failed().SetMsg("视频详细信息不存在，请先同步视频数据").Regin(c)
		return
	}

	gf.Log().Info(gfctx, "抖音视频信息查询成功", gf.Map{
		"user_uuid":   userUUID,
		"record_uuid": videoRelated.UUID,
		"aweme_id":    douyinAweme.AwemeID,
		"title":       douyinAweme.Title,
		"author":      douyinAweme.Nickname,
	})

	// 4. 检查是否已经存在相同的 assets 记录
	var existingAsset entity.Assets
	err = tx.Model("assets").
		Where("user_uuid", userUUID).
		Where("video_id", douyinAweme.AwemeID).
		Where("is_deleted", 0).
		Scan(&existingAsset)

	if err == nil && existingAsset.UUID != "" {
		tx.Rollback()
		gf.Log().Warning(gfctx, "素材记录已存在", gf.Map{
			"user_uuid":     userUUID,
			"record_uuid":   videoRelated.UUID,
			"aweme_id":      douyinAweme.AwemeID,
			"existing_uuid": existingAsset.UUID,
		})
		gf.Failed().SetMsg("该视频已经添加到素材库中").Regin(c)
		return
	}

	// 5. 创建 assets 记录
	// 使用 mapper 创建基础数据
	fullURL := fmt.Sprintf("https://www.douyin.com/video/%s", douyinAweme.AwemeID)
	assetData := assets_mapper.FromDouyinAweme(&douyinAweme, fullURL)

	// 创建完整的 asset 对象，包含额外的字段
	asset := &entity.Assets{
		BaseEntity: entity.BaseEntity{
			UUID: gf.GenerateUUID(),
		},
		UserUUID:         userUUID,
		VideoCover:       douyinAweme.CoverURL,
		Source:           getSourceByType(videoRelated.SourceType),
		AnalysisStatus:   cons.Preprocessing.Value,
		GenerationStatus: cons.Pending.Value,
		Insight:          getInsightBySourceType(videoRelated.SourceType, videoRelated.SourceId),
	}

	// 使用 mergo 合并 mapper 生成的数据
	if err := mergo.Merge(asset, assetData, mergo.WithOverride); err != nil {
		tx.Rollback()
		gf.Log().Error(gfctx, "合并asset数据失败", gf.Map{
			"error":       err.Error(),
			"user_uuid":   userUUID,
			"record_uuid": videoRelated.UUID,
			"aweme_id":    douyinAweme.AwemeID,
		})
		gf.Failed().SetMsg("创建素材记录失败").Regin(c)
		return
	}

	gf.Log().Info(gfctx, "准备创建素材记录", gf.Map{
		"user_uuid":   userUUID,
		"record_uuid": videoRelated.UUID,
		"asset_uuid":  asset.UUID,
		"aweme_id":    asset.VideoID,
		"title":       asset.VideoTitle,
		"source":      asset.Source,
		"insight":     asset.Insight,
	})

	// 6. 插入 assets 记录
	_, err = tx.Model("assets").Insert(asset)
	if err != nil {
		tx.Rollback()
		gf.Log().Error(gfctx, "创建素材记录失败", gf.Map{
			"error":       err.Error(),
			"user_uuid":   userUUID,
			"record_uuid": videoRelated.UUID,
			"asset_uuid":  asset.UUID,
		})
		gf.Failed().SetMsg("创建素材记录失败").Regin(c)
		return
	}

	// 7. 更新 user_inbox_video_related 记录状态为 'SUCCESS'
	_, err = tx.Model("user_inbox_video_related").
		Where("uuid", videoRelated.UUID).
		Data(gf.Map{
			"handle_status": cons.Success.Value,
			"update_time":   time.Now(),
		}).Update()

	if err != nil {
		tx.Rollback()
		gf.Log().Error(gfctx, "更新视频关联记录状态失败", gf.Map{
			"error":       err.Error(),
			"user_uuid":   userUUID,
			"record_uuid": videoRelated.UUID,
		})
		gf.Failed().SetMsg("更新记录状态失败").Regin(c)
		return
	}

	// 8. 提交事务
	err = tx.Commit()
	if err != nil {
		gf.Log().Error(gfctx, "提交事务失败", gf.Map{
			"error":       err.Error(),
			"user_uuid":   userUUID,
			"record_uuid": videoRelated.UUID,
			"asset_uuid":  asset.UUID,
		})
		gf.Failed().SetMsg("保存数据失败").Regin(c)
		return
	}

	gf.Log().Info(gfctx, "视频添加到素材库成功", gf.Map{
		"user_uuid":   userUUID,
		"record_uuid": videoRelated.UUID,
		"asset_uuid":  asset.UUID,
		"aweme_id":    asset.VideoID,
		"title":       asset.VideoTitle,
	})

	// 9. 获取用户信息用于工作流处理
	var user entity.User
	err = gf.Model("user").Scan(&user, "uuid", userUUID)
	if err != nil {
		gf.Log().Error(gfctx, "获取用户信息失败", gf.Map{
			"error":     err.Error(),
			"user_uuid": userUUID,
		})
		gf.Failed().SetMsg("获取用户信息失败").Regin(c)
		return
	}

	// 10. 创建或获取 Eino 执行记录并启动工作流
	einoExecuteRecord, err := workflow.GetOrCreateEinoExecuteRecord(asset, cons.EinoExecuteAsset.Value)
	if err != nil {
		gf.Log().Error(gfctx, "创建Eino执行记录失败", gf.Map{
			"error":      err.Error(),
			"user_uuid":  userUUID,
			"asset_uuid": asset.UUID,
		})
		gf.Failed().SetMsg(err.Error()).Regin(c)
		return
	}

	einoExecuteRecord.HandleStatus = &cons.Processing.Value
	go func() {
		workflow.HandleWorkflow(asset, &user, einoExecuteRecord)
	}()

	gf.Log().Info(gfctx, "工作流已启动", gf.Map{
		"user_uuid":           userUUID,
		"asset_uuid":          asset.UUID,
		"eino_execute_id":     einoExecuteRecord.UUID,
		"eino_execute_status": *einoExecuteRecord.HandleStatus,
	})

	// 11. 返回成功响应
	gf.Success().SetData(asset).SetMsg("视频已成功添加到素材库").Regin(c)
}

// getSourceByType 根据来源类型获取对应的 Source 值
func getSourceByType(sourceType entity.SourceType) string {
	switch sourceType {
	case entity.SourceTypeCollect:
		return cons.Collection.Value
	case entity.SourceTypeKeyword:
		return cons.Manual.Value
	case entity.SourceTypeAuthor:
		return cons.Manual.Value
	default:
		return cons.Manual.Value
	}
}

// getInsightBySourceType 根据来源类型和来源ID获取对应的 Insight 值
func getInsightBySourceType(sourceType entity.SourceType, sourceId string) string {
	var gfctx = gctx.New()

	switch sourceType {
	case entity.SourceTypeCollect:
		// 查询收藏夹名称
		var collect entity.DouyinCollect
		err := gf.Model("douyin_collects").
			Where("collects_id", sourceId).
			Scan(&collect)
		if err == nil && collect.CollectsName != "" {
			return collect.CollectsName
		}
		return "收藏夹视频"
	case entity.SourceTypeKeyword:
		// 查询关键词名称
		var keyword entity.TrendInsightKeyword
		err := setting.CrawlerModel(&keyword).
			Where("id", sourceId).
			Scan(&keyword)
		if err == nil && keyword.Keyword != "" {
			return fmt.Sprintf("关键词: %s", keyword.Keyword)
		}
		return "关键词视频"
	case entity.SourceTypeAuthor:
		// 查询作者名称
		var author entity.TrendInsightAuthor
		err := setting.CrawlerModel(&author).
			Where("id", sourceId).
			Scan(&author)
		if err == nil && author.UserName != "" {
			return fmt.Sprintf("作者: %s", author.UserName)
		}
		return "作者视频"
	default:
		gf.Log().Warning(gfctx, "未知的来源类型", gf.Map{
			"source_type": string(sourceType),
			"source_id":   sourceId,
		})
		return "未知来源"
	}
}

// GetAuthorVideos 根据趋势洞察用户ID查询该作者发布的所有视频
// @Summary 根据趋势洞察用户ID查询作者发布的视频
// @Description 根据trendinsight_user_id和user_uuid查询user_inbox_video_related表获取数据，然后关联查询douyin_aweme表获取视频详细信息。支持分页查询，按发布时间倒序排列。适用于查询用户收件箱中特定作者的视频数据。
// @Tags 抖音数据查询
// @Accept json
// @Produce json
// @Param trendinsight_user_id query string true "趋势洞察用户ID，用于标识特定的抖音作者，通常以MS4wLjABAAAA开头的字符串" minlength(5) maxlength(64) example("MS4wLjABAAAA1234567890abcdef")
// @Param page query int false "页码，从1开始，用于分页查询。当数据量较大时建议使用分页" minimum(1) default(1) example(1)
// @Param page_size query int false "每页数量，范围1-100，控制单次返回的视频数量。建议根据实际需求调整，避免单次请求数据过多" minimum(1) maximum(100) default(20) example(20)
// @Success 200 {object} object{code=int,msg=string,data=object{list=[]entity.UserInboxVideoRelatedWithAweme,total=int64,page=int,page_size=int,total_pages=int},token=string,time=int64} "查询成功"
// @Success 200 {object} object{code=int,msg=string,data=object{list=[]entity.UserInboxVideoRelatedWithAweme,total=int64,page=int,page_size=int,total_pages=int},token=string,time=int64} "查询成功响应示例，data.list包含UserInboxVideoRelatedWithAweme实体数组，包含用户收件箱视频关联信息和抖音视频详细信息"
// @Failure 400 {object} object{code=int,msg=string,data=object{},time=int64} "请求参数错误"
// @Failure 400 {object} object{code=int,msg=string,data=object{},time=int64} "趋势洞察用户ID参数不能为空"
// @Failure 400 {object} object{code=int,msg=string,data=object{},time=int64} "趋势洞察用户ID格式不正确，长度应在5-64字符之间"
// @Failure 400 {object} object{code=int,msg=string,data=object{},time=int64} "页码参数无效，必须大于0"
// @Failure 400 {object} object{code=int,msg=string,data=object{},time=int64} "每页数量参数无效，范围：1-100"
// @Failure 401 {object} object{code=int,msg=string,data=object{},time=int64} "用户未登录，请先进行身份认证"
// @Failure 500 {object} object{code=int,msg=string,data=object{},time=int64} "服务器内部错误，查询数据库失败"
// @Security BearerAuth
// @Router /client/user/douyin/getAuthorVideos [get]
func (api *Douyin) GetAuthorVideos(c *gf.GinCtx) {
	var gfctx = gctx.New()

	// 获取当前用户信息
	getuser, exists := c.Get("user")
	if !exists {
		gf.Failed().SetMsg("用户信息获取失败").Regin(c)
		return
	}
	userOBJ := getuser.(gf.UserObj)
	userUUID := string(userOBJ.UserUUID)

	// 获取查询参数
	trendinsightUserID := c.Query("trendinsight_user_id")
	pageStr := c.DefaultQuery("page", "1")
	pageSizeStr := c.DefaultQuery("page_size", "20")

	// 参数验证
	if trendinsightUserID == "" {
		gf.Failed().SetMsg("趋势洞察用户ID参数不能为空").Regin(c)
		return
	}

	// 验证趋势洞察用户ID格式（基本长度检查）
	if len(trendinsightUserID) < 5 || len(trendinsightUserID) > 64 {
		gf.Failed().SetMsg("趋势洞察用户ID格式不正确").Regin(c)
		return
	}

	// 解析分页参数
	page, err := strconv.Atoi(pageStr)
	if err != nil || page < 1 {
		gf.Failed().SetMsg("页码参数无效，必须大于0").Regin(c)
		return
	}

	pageSize, err := strconv.Atoi(pageSizeStr)
	if err != nil || pageSize < 1 || pageSize > 100 {
		gf.Failed().SetMsg("每页数量参数无效，范围：1-100").Regin(c)
		return
	}

	gf.Log().Info(gfctx, "开始查询作者视频（从用户收件箱）", gf.Map{
		"user_uuid":            userUUID,
		"trendinsight_user_id": trendinsightUserID,
		"page":                 page,
		"page_size":            pageSize,
	})

	// 1. 先查询 user_inbox_video_related 表中该用户和作者的视频关联记录总数
	// 使用 source_type='author' 和 source_id=trendinsightUserID 进行查询
	totalCount, err := gf.Model("user_inbox_video_related").
		Where("user_uuid", userUUID).
		Where("source_id", trendinsightUserID).
		Where("source_type", "author").
		Where("is_deleted", false).
		Count()

	if err != nil {
		gf.Log().Error(gfctx, "查询用户收件箱作者视频总数失败", gf.Map{
			"error":                err.Error(),
			"user_uuid":            userUUID,
			"trendinsight_user_id": trendinsightUserID,
		})
		gf.Failed().SetMsg("查询作者视频总数失败").Regin(c)
		return
	}

	// 如果没有视频关联记录，直接返回空结果
	if totalCount == 0 {
		gf.Log().Info(gfctx, "用户收件箱中没有该作者的视频", gf.Map{
			"user_uuid":            userUUID,
			"trendinsight_user_id": trendinsightUserID,
		})

		responseData := gf.Map{
			"list":        []interface{}{},
			"total":       int64(0),
			"page":        page,
			"page_size":   pageSize,
			"total_pages": 0,
		}
		gf.Success().SetData(responseData).SetMsg("查询成功，用户收件箱中暂无该作者的视频").Regin(c)
		return
	}

	// 2. 分页查询用户收件箱视频关联记录
	var videoRelatedList []*entity.UserInboxVideoRelated
	offset := (page - 1) * pageSize

	err = gf.Model("user_inbox_video_related").
		Where("user_uuid", userUUID).
		Where("source_id", trendinsightUserID).
		Where("source_type", "author").
		Where("is_deleted", false).
		Order("publish_time DESC, create_time DESC"). // 按发布时间倒序排列
		Offset(offset).
		Limit(pageSize).
		Scan(&videoRelatedList)

	if err != nil {
		gf.Log().Error(gfctx, "查询用户收件箱作者视频列表失败", gf.Map{
			"error":                err.Error(),
			"user_uuid":            userUUID,
			"trendinsight_user_id": trendinsightUserID,
			"page":                 page,
			"page_size":            pageSize,
			"offset":               offset,
		})
		gf.Failed().SetMsg("查询作者视频列表失败").Regin(c)
		return
	}

	// 如果没有查询到记录，返回空结果
	if len(videoRelatedList) == 0 {
		responseData := gf.Map{
			"list":        []interface{}{},
			"total":       totalCount,
			"page":        page,
			"page_size":   pageSize,
			"total_pages": (int(totalCount) + pageSize - 1) / pageSize,
		}
		gf.Success().SetData(responseData).SetMsg("查询成功").Regin(c)
		return
	}

	// 3. 提取所有的 aweme_id
	awemeIDs := make([]string, len(videoRelatedList))
	for i, record := range videoRelatedList {
		awemeIDs[i] = record.AwemeId
	}

	gf.Log().Info(gfctx, "提取aweme_id列表", gf.Map{
		"user_uuid":            userUUID,
		"trendinsight_user_id": trendinsightUserID,
		"aweme_count":          len(awemeIDs),
		"aweme_ids":            awemeIDs,
	})

	// 4. 从 media_crawler 数据库查询对应的抖音视频信息
	var douyinAwemeMap = make(map[string]*entity.DouyinAweme)
	if len(awemeIDs) > 0 {
		var douyinAwemeList []entity.DouyinAweme
		err = setting.CrawlerModel(&entity.DouyinAweme{}).
			Where("aweme_id IN (?)", awemeIDs).
			Scan(&douyinAwemeList)

		if err != nil {
			gf.Log().Error(gfctx, "查询抖音视频详细信息失败", gf.Map{
				"error":                err.Error(),
				"user_uuid":            userUUID,
				"trendinsight_user_id": trendinsightUserID,
				"aweme_ids":            awemeIDs,
			})
			// 即使查询失败，也返回基础数据，只是没有详细的视频信息
		} else {
			// 创建 aweme_id -> DouyinAweme 的映射
			for i := range douyinAwemeList {
				douyinAwemeMap[douyinAwemeList[i].AwemeID] = &douyinAwemeList[i]
			}
			gf.Log().Info(gfctx, "查询抖音视频详细信息成功", gf.Map{
				"user_uuid":            userUUID,
				"trendinsight_user_id": trendinsightUserID,
				"found_count":          len(douyinAwemeList),
				"total_aweme":          len(awemeIDs),
			})
		}
	}

	// 5. 构建最终结果，将用户收件箱视频关联记录与抖音视频详细信息组合
	var resultList []*entity.UserInboxVideoRelatedWithAweme
	for _, record := range videoRelatedList {
		result := &entity.UserInboxVideoRelatedWithAweme{
			UserInboxVideoRelated: *record,
		}

		// 如果找到对应的抖音视频信息，则关联
		if aweme, exists := douyinAwemeMap[record.AwemeId]; exists {
			result.DouyinAweme = aweme
		}

		resultList = append(resultList, result)
	}

	// 计算总页数
	totalPages := (int(totalCount) + pageSize - 1) / pageSize

	// 构建响应数据
	responseData := gf.Map{
		"list":        resultList,
		"total":       totalCount,
		"page":        page,
		"page_size":   pageSize,
		"total_pages": totalPages,
	}

	gf.Log().Info(gfctx, "查询作者视频成功", gf.Map{
		"user_uuid":            userUUID,
		"trendinsight_user_id": trendinsightUserID,
		"page":                 page,
		"page_size":            pageSize,
		"total":                totalCount,
		"result_count":         len(resultList),
		"total_pages":          totalPages,
		"aweme_matched":        len(douyinAwemeMap),
	})

	gf.Success().SetData(responseData).SetMsg("查询作者视频成功").Regin(c)
}
