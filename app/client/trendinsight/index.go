package trendinsight

import (
	"context"
	"fmt"
	"gofly/app/client/cons"
	"gofly/app/client/entity"
	"gofly/app/client/entity/services"

	client "github.com/qihaozhushou/mediacrawler-client"

	"gofly/setting"
	"gofly/utils/gf"
	"gofly/utils/tools/gcfg"
	"gofly/utils/tools/gconv"
	"gofly/utils/tools/gctx"
	"strings"
	"time"
)

// Index TrendInsight 主控制器
type Index struct {
	NoNeedLogin []string // 忽略登录接口配置-忽略全部传["*"]
	NoNeedAuths []string // 忽略RBAC权限认证接口配置-忽略全部传["*"]

	// 服务依赖
	userKeywordService *services.UserInboxSourceRelatedService
}

func init() {
	fpath := Index{
		NoNeedLogin: []string{"debugUserKeywords", "testDatabaseData"}, // 调试接口不需要登录
		NoNeedAuths: []string{"debugUserKeywords", "testDatabaseData"}, // 调试接口不需要权限验证

		// 初始化服务
		userKeywordService: services.NewUserInboxSourceRelatedService(),
	}
	gf.Register(&fpath, fpath)
}

// SearchVideos 搜索巨量引擎平台视频
// @Summary 搜索巨量引擎平台视频
// @Description 通过关键词搜索巨量引擎平台的视频内容，支持多种筛选条件
// @Tags TrendInsight视频搜索
// @Accept json
// @Produce json
// @Param keyword query string true "搜索关键词" example("人工智能")
// @Param type query string false "搜索类型" Enums(video,author) default(video) example("video")
// @Param author_ids query array false "作者ID列表（逗号分隔）" collectionFormat(csv)
// @Param category_id query string false "分类ID" example("1")
// @Param date_type query integer false "日期类型 (0-3)" minimum(0) maximum(3) example(1)
// @Param label_type query integer false "标签类型 (0-2)" minimum(0) maximum(2) example(1)
// @Param duration_type query integer false "时长类型 (0-3)" minimum(0) maximum(3) example(2)
// @Success 200 {object} object{code=int,msg=string,data=object{videos=[]TrendInsightVideoResponseSchema,keyword=string,type=string,status=string,msg=string,video_count=int}} "搜索成功"
// @Failure 400 {object} object{code=int,msg=string,data=object{}} "请求参数错误"
// @Failure 401 {object} object{code=int,msg=string,data=object{}} "未授权"
// @Failure 500 {object} object{code=int,msg=string,data=object{}} "服务器内部错误"
// @Security BearerAuth
// @Router /client/trendinsight/index/SearchVideos [get]
func (api *Index) SearchVideos(c *gf.GinCtx) {
	param, _ := gf.RequestParam(c)

	// 验证必需参数
	keyword, ok := param["keyword"]
	if !ok || keyword == nil {
		gf.Failed().SetMsg("搜索关键词不能为空").Regin(c)
		return
	}

	keywordStr := keyword.(string)
	if strings.TrimSpace(keywordStr) == "" {
		gf.Failed().SetMsg("搜索关键词不能为空").Regin(c)
		return
	}

	if len(keywordStr) > 100 {
		gf.Failed().SetMsg("搜索关键词长度不能超过100个字符").Regin(c)
		return
	}

	// 处理 type 参数，默认为 "video"
	typeStr := "video"
	if typeParam, ok := param["type"]; ok && typeParam != nil {
		if typeParamStr, ok := typeParam.(string); ok {
			// 验证 type 参数值
			if typeParamStr == "video" || typeParamStr == "author" {
				typeStr = typeParamStr
			} else {
				gf.Failed().SetMsg("type 参数值无效，只支持 'video' 或 'author'").Regin(c)
				return
			}
		}
	}

	// 创建客户端并调用API
	client, err := api.createTrendInsightClient()
	if err != nil {
		gf.Failed().SetMsg("创建TrendInsight客户端失败: " + err.Error()).Regin(c)
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// 构建搜索请求
	searchReq := client.TrendInsightAPIAPI.SearchVideosByKeyword(ctx).
		Keyword(keywordStr)

	// 处理可选参数
	if authorIds, ok := param["author_ids"]; ok && authorIds != nil {
		if authorIdsSlice, ok := authorIds.([]interface{}); ok {
			var authorIdStrs []string
			for _, id := range authorIdsSlice {
				if idStr, ok := id.(string); ok {
					authorIdStrs = append(authorIdStrs, idStr)
				}
			}
			if len(authorIdStrs) > 0 {
				// 将字符串数组转换为逗号分隔的字符串
				authorIdsStr := strings.Join(authorIdStrs, ",")
				searchReq = searchReq.AuthorIds(authorIdsStr)
			}
		}
	}

	if categoryId, ok := param["category_id"]; ok && categoryId != nil {
		if categoryIdStr, ok := categoryId.(string); ok {
			searchReq = searchReq.CategoryId(categoryIdStr)
		}
	}

	if dateType, ok := param["date_type"]; ok && dateType != nil {
		if dateTypeFloat, ok := dateType.(float64); ok {
			dateTypeInt := int64(dateTypeFloat)
			if dateTypeInt >= 0 && dateTypeInt <= 3 {
				searchReq = searchReq.DateType(dateTypeInt)
			}
		}
	}

	if labelType, ok := param["label_type"]; ok && labelType != nil {
		if labelTypeFloat, ok := labelType.(float64); ok {
			labelTypeInt := int64(labelTypeFloat)
			if labelTypeInt >= 0 && labelTypeInt <= 2 {
				searchReq = searchReq.LabelType(labelTypeInt)
			}
		}
	}

	if durationType, ok := param["duration_type"]; ok && durationType != nil {
		if durationTypeFloat, ok := durationType.(float64); ok {
			durationTypeInt := int64(durationTypeFloat)
			if durationTypeInt >= 0 && durationTypeInt <= 3 {
				searchReq = searchReq.DurationType(durationTypeInt)
			}
		}
	}

	// 执行搜索请求
	resp, _, err := searchReq.Execute()
	if err != nil {
		gf.Failed().SetMsg("视频搜索请求失败: " + err.Error()).Regin(c)
		return
	}

	// 检查响应
	if resp == nil {
		gf.Failed().SetMsg("搜索响应为空").Regin(c)
		return
	}

	// 获取视频数据
	videoData := resp.GetData().Data

	// 添加日志记录，标明这是视频类型的搜索
	gf.Log().Info(context.Background(), "TrendInsight视频搜索响应:", gf.Map{
		"keyword":     keywordStr,
		"type":        typeStr,
		"status":      resp.GetStatus(),
		"msg":         resp.GetMsg(),
		"video_count": len(videoData),
	})

	// 解析并结构化响应数据
	result := gf.Map{
		"videos":      videoData,
		"keyword":     keywordStr,
		"type":        typeStr,
		"status":      resp.GetStatus(),
		"msg":         resp.GetMsg(),
		"video_count": len(videoData),
	}

	gf.Success().SetMsg("视频搜索成功").SetData(result).Regin(c)
}

// SearchAuthors 搜索巨量引擎平台创作者
// @Summary 搜索巨量引擎平台创作者
// @Description 通过关键词搜索巨量引擎平台的创作者信息
// @Tags TrendInsight作者搜索
// @Accept json
// @Produce json
// @Param keyword query string true "搜索关键词" example("科技博主")
// @Param type query string false "搜索类型" Enums(author,video) default(author) example("author")
// @Param total query integer false "返回结果数量限制" minimum(1) maximum(100) example(20)
// @Success 200 {object} object{code=int,msg=string,data=object{authors=[]TrendInsightAuthorSchema,keyword=string,type=string,status=string,msg=string,author_count=int}} "搜索成功"
// @Failure 400 {object} object{code=int,msg=string,data=object{}} "请求参数错误"
// @Failure 401 {object} object{code=int,msg=string,data=object{}} "未授权"
// @Failure 500 {object} object{code=int,msg=string,data=object{}} "服务器内部错误"
// @Security BearerAuth
// @Router /client/trendinsight/index/SearchAuthors [get]
func (api *Index) SearchAuthors(c *gf.GinCtx) {
	param, _ := gf.RequestParam(c)

	// 验证必需参数
	keyword, ok := param["keyword"]
	if !ok || keyword == nil {
		gf.Failed().SetMsg("搜索关键词不能为空").Regin(c)
		return
	}

	keywordStr := keyword.(string)
	if strings.TrimSpace(keywordStr) == "" {
		gf.Failed().SetMsg("搜索关键词不能为空").Regin(c)
		return
	}

	if len(keywordStr) > 100 {
		gf.Failed().SetMsg("搜索关键词长度不能超过100个字符").Regin(c)
		return
	}

	// 处理 type 参数，默认为 "author"
	typeStr := "author"
	if typeParam, ok := param["type"]; ok && typeParam != nil {
		if typeParamStr, ok := typeParam.(string); ok {
			// 验证 type 参数值
			if typeParamStr == "author" || typeParamStr == "video" {
				typeStr = typeParamStr
			} else {
				gf.Failed().SetMsg("type 参数值无效，只支持 'author' 或 'video'").Regin(c)
				return
			}
		}
	}

	// 创建客户端并调用API
	client, err := api.createTrendInsightClient()
	if err != nil {
		gf.Failed().SetMsg("创建TrendInsight客户端失败: " + err.Error()).Regin(c)
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// 构建搜索请求
	searchReq := client.TrendInsightAPIAPI.SearchDarenUsers(ctx).
		Keyword(keywordStr)

	// 处理可选参数
	if total, ok := param["total"]; ok && total != nil {
		if totalFloat, ok := total.(float64); ok {
			totalInt := int(totalFloat)
			if totalInt > 0 && totalInt <= 100 {
				searchReq = searchReq.Total(int64(totalInt))
			}
		}
	}

	// 执行搜索请求
	resp, _, err := searchReq.Execute()
	if err != nil {
		gf.Failed().SetMsg("创作者搜索请求失败: " + err.Error()).Regin(c)
		return
	}

	// 检查响应
	if resp == nil {
		gf.Failed().SetMsg("搜索响应为空").Regin(c)
		return
	}

	// 添加日志记录，标明这是作者类型的搜索
	// 获取作者数据
	authorData := resp.GetData().Userlist

	gf.Log().Info(context.Background(), "TrendInsight作者搜索响应:", gf.Map{
		"keyword":      keywordStr,
		"type":         typeStr,
		"status":       resp.GetStatus(),
		"msg":          resp.GetMsg(),
		"author_count": len(authorData),
	})

	// 处理作者数据，转换为安全的格式
	var authors []gf.Map
	if authorData != nil {
		for _, author := range authorData {
			authorMap := gf.Map{}

			// 处理 NullableString 字段
			if author.UserId.IsSet() {
				authorMap["user_id"] = author.UserId.Get()
			}
			if author.UserName.IsSet() {
				authorMap["user_name"] = author.UserName.Get()
			}
			if author.ItemCount.IsSet() {
				authorMap["item_count"] = author.ItemCount.Get()
			}
			if author.FollowCount.IsSet() {
				authorMap["follow_count"] = author.FollowCount.Get()
			}
			if author.LikeCount.IsSet() {
				authorMap["like_count"] = author.LikeCount.Get()
			}
			if author.UserHeadLogo.IsSet() {
				authorMap["user_head_logo"] = author.UserHeadLogo.Get()
			}
			if author.FirstTagName.IsSet() {
				authorMap["first_tag_name"] = author.FirstTagName.Get()
			}
			if author.SecondTagName.IsSet() {
				authorMap["second_tag_name"] = author.SecondTagName.Get()
			}
			if author.AwemeId.IsSet() {
				authorMap["aweme_id"] = author.AwemeId.Get()
			}
			if author.AwemeUrl.IsSet() {
				authorMap["aweme_url"] = author.AwemeUrl.Get()
			}

			authors = append(authors, authorMap)
		}
	}

	// 解析并结构化响应数据
	result := gf.Map{
		"authors":      authors,
		"keyword":      keywordStr,
		"type":         typeStr,
		"status":       resp.GetStatus(),
		"msg":          resp.GetMsg(),
		"author_count": len(authors),
	}

	gf.Success().SetMsg("创作者搜索成功").SetData(result).Regin(c)
}

// createTrendInsightClient 创建TrendInsight客户端
func (api *Index) createTrendInsightClient() (*client.APIClient, error) {
	var gfctx = gctx.New()
	baseUrl, err := gcfg.Instance().Get(gfctx, "mediaCrawler.default.baseUrl")
	if err != nil {
		return nil, fmt.Errorf("获取MediaCrawler配置失败: %w", err)
	}

	baseUrlStr := baseUrl.String()
	if baseUrlStr == "" {
		baseUrlStr = "http://localhost:8000" // 默认地址
	}

	// 创建 OpenAPI 客户端配置
	cfg := client.NewConfiguration()
	cfg.Host = strings.TrimPrefix(strings.TrimPrefix(baseUrlStr, "http://"), "https://")
	if strings.HasPrefix(baseUrlStr, "https://") {
		cfg.Scheme = "https"
	} else {
		cfg.Scheme = "http"
	}

	apiClient := client.NewAPIClient(cfg)
	return apiClient, nil
}

// createMediaCrawlerClient 创建MediaCrawler客户端
func (api *Index) createMediaCrawlerClient() (*client.APIClient, error) {
	var gfctx = gctx.New()
	baseUrl, err := gcfg.Instance().Get(gfctx, "mediaCrawler.default.baseUrl")
	if err != nil {
		return nil, fmt.Errorf("获取MediaCrawler配置失败: %w", err)
	}

	baseUrlStr := baseUrl.String()
	if baseUrlStr == "" {
		baseUrlStr = "http://localhost:8000" // 默认地址
	}

	// 创建 OpenAPI 客户端配置
	cfg := client.NewConfiguration()
	cfg.Host = strings.TrimPrefix(strings.TrimPrefix(baseUrlStr, "http://"), "https://")
	if strings.HasPrefix(baseUrlStr, "https://") {
		cfg.Scheme = "https"
	} else {
		cfg.Scheme = "http"
	}

	apiClient := client.NewAPIClient(cfg)
	return apiClient, nil
}

// GetUserVideoKeywords 获取用户视频关键词监控列表
func (api *Index) GetUserVideoKeywords(c *gf.GinCtx) {
	api.getUserKeywordsByType(c, string(entity.SourceTypeKeyword))
}

// GetUserAuthorKeywords 获取用户作者关键词监控列表
func (api *Index) GetUserAuthorKeywords(c *gf.GinCtx) {
	api.getUserKeywordsByType(c, "author")
}

// getUserKeywordsByType 通用的获取用户关键词方法（内部使用）
func (api *Index) getUserKeywordsByType(c *gf.GinCtx, keywordType string) {
	// 获取用户UUID
	userObj, exists := c.Get("user")
	if !exists {
		gf.Failed().SetMsg("用户未登录").SetCode(401).Regin(c)
		return
	}
	userUUID := string(userObj.(gf.UserObj).UserUUID)

	param, _ := gf.RequestParam(c)

	// 处理分页参数
	page := 1
	pageSize := 10

	if pageParam, ok := param["page"]; ok && pageParam != nil {
		if pageFloat, ok := pageParam.(float64); ok {
			page = int(pageFloat)
		}
	}

	if pageSizeParam, ok := param["page_size"]; ok && pageSizeParam != nil {
		if pageSizeFloat, ok := pageSizeParam.(float64); ok {
			pageSize = int(pageSizeFloat)
		}
	}

	// 按指定类型获取关键词列表
	userKeywords, total, err := api.userKeywordService.GetUserKeywordsByType(userUUID, keywordType, page, pageSize)
	if err != nil {
		gf.Failed().SetMsg(fmt.Sprintf("获取用户%s关键词失败: %s", keywordType, err.Error())).Regin(c)
		return
	}

	// 构建响应数据 - 根据类型返回不同的关联信息
	keywordList := make([]gf.Map, 0, len(userKeywords))
	for _, uk := range userKeywords {
		keywordData := gf.Map{
			"id":         uk.UUID,
			"source_id":  uk.SourceId,
			"user_uuid":  uk.UserUUID,
			"type":       uk.SourceType,
			"created_at": uk.CreateTime,
			"updated_at": uk.UpdateTime,
		}

		// 根据类型添加不同的关联信息
		if keywordType == string(entity.SourceTypeKeyword) {
			keywordData["trend_keyword"] = uk.TrendKeyword
			// 添加显示名称
			if uk.TrendKeyword != nil {
				keywordData["display_name"] = uk.TrendKeyword.Keyword
			}
		} else if keywordType == string(entity.SourceTypeAuthor) {
			keywordData["trend_author"] = uk.TrendAuthor
			// 添加显示名称
			if uk.TrendAuthor != nil {
				keywordData["display_name"] = uk.TrendAuthor.UserName
			}
		}

		// 通用显示名称（兼容性）
		keywordData["name"] = uk.GetDisplayName()

		keywordList = append(keywordList, keywordData)
	}

	// 计算分页信息
	totalPages := (int(total) + pageSize - 1) / pageSize
	hasMore := page < totalPages

	result := gf.Map{
		"keywords":    keywordList,
		"total":       total,
		"page":        page,
		"page_size":   pageSize,
		"total_pages": totalPages,
		"has_more":    hasMore,
	}

	gf.Success().SetMsg(fmt.Sprintf("获取%s关键词监控列表成功", keywordType)).SetData(result).Regin(c)
}

// DelUserKeywordParId 删除用户关键词关联
func (api *Index) DelUserKeywordParId(c *gf.GinCtx) {
	// 获取用户UUID
	userObj, exists := c.Get("user")
	if !exists {
		gf.Failed().SetMsg("用户未登录").SetCode(401).Regin(c)
		return
	}
	userUUID := string(userObj.(gf.UserObj).UserUUID)

	// 从路径参数获取 id (Gofly 框架自动映射为 :id)
	keywordUUID := c.Param("id")
	if strings.TrimSpace(keywordUUID) == "" {
		gf.Failed().SetMsg("关键词UUID不能为空").Regin(c)
		return
	}

	// 通过UUID获取用户关键词关联
	userKeyword, err := api.userKeywordService.GetByUUID(keywordUUID)
	if err != nil {
		gf.Failed().SetMsg("获取关键词关联失败: " + err.Error()).Regin(c)
		return
	}

	// 检查关键词关联是否存在
	if userKeyword == nil || userKeyword.UUID == "" {
		gf.Failed().SetMsg("关键词关联不存在").SetCode(404).Regin(c)
		return
	}

	// 验证用户权限 - 只能删除自己的关键词关联
	if userKeyword.UserUUID != userUUID {
		gf.Failed().SetMsg("无权删除该关键词").SetCode(403).Regin(c)
		return
	}

	// 检查关键词是否已被删除
	if userKeyword.IsDeleted() {
		gf.Failed().SetMsg("关键词已被删除").Regin(c)
		return
	}

	// 执行软删除
	err = api.userKeywordService.DeleteByUUID(userKeyword.UUID)
	if err != nil {
		gf.Failed().SetMsg("删除关键词失败: " + err.Error()).Regin(c)
		return
	}

	// 构建返回数据
	result := gf.Map{
		"keyword_uuid": userKeyword.UUID,
		"source_id":    userKeyword.SourceId,
		"display_name": userKeyword.GetDisplayName(),
		"deleted_at":   time.Now(),
		"message":      "关键词删除成功",
	}

	gf.Success().SetMsg("删除关键词成功").SetData(result).Regin(c)
}

// UpsertVideoKeywords 创建或更新视频关键词
// @Summary 创建或更新视频关键词
// @Description 创建或更新单个视频关键词到 MediaCrawler 服务
// @Tags 关键词管理
// @Accept json
// @Produce json
// @Param request body object true "关键词请求"
// @Param request.keyword body string true "关键词" example("人工智能")
// @Success 200 {object} object "成功响应"
// @Success 200 {object} object{code=int,msg=string,data=object{keyword=string,user_keyword=object,message=string}} "成功响应示例"
// @Failure 400 {object} object{code=int,msg=string,data=object{}} "请求参数错误"
// @Failure 401 {object} object{code=int,msg=string,data=object{}} "未授权"
// @Failure 500 {object} object{code=int,msg=string,data=object{}} "服务器内部错误"
// @Security BearerAuth
// @Router /client/trendinsight/index/UpsertVideoKeywords [post]
func (api *Index) UpsertVideoKeywords(c *gf.GinCtx) {
	api.upsertKeywordsByType(c, string(entity.SourceTypeKeyword))
}

// UpsertAuthorKeywords 创建或更新作者关键词
// @Summary 创建或更新作者关键词
// @Description 通过单个用户ID创建或更新作者关键词到 MediaCrawler 服务
// @Tags 关键词管理
// @Accept json
// @Produce json
// @Param request body object true "用户ID请求"
// @Param request.user_id body string true "用户ID" example("douyin_123456")
// @Success 200 {object} object "成功响应"
// @Success 200 {object} object{code=int,msg=string,data=object{user_id=string,user_keyword=object,message=string}} "成功响应示例"
// @Failure 400 {object} object{code=int,msg=string,data=object{}} "请求参数错误"
// @Failure 401 {object} object{code=int,msg=string,data=object{}} "未授权"
// @Failure 500 {object} object{code=int,msg=string,data=object{}} "服务器内部错误"
// @Security BearerAuth
// @Router /client/trendinsight/index/UpsertAuthorKeywords [post]
func (api *Index) UpsertAuthorKeywords(c *gf.GinCtx) {
	api.upsertKeywordsByType(c, "author")
}

// upsertKeywordsByType 通用的创建/更新关键词方法（内部使用）
func (api *Index) upsertKeywordsByType(c *gf.GinCtx, keywordType string) {
	// 根据类型定义不同的请求参数结构
	if keywordType == string(entity.SourceTypeKeyword) {
		// 关键词类型：使用单个关键词
		var req struct {
			Keyword string `json:"keyword" binding:"required"`
		}

		if err := c.ShouldBindJSON(&req); err != nil {
			gf.Failed().SetMsg("参数错误: " + err.Error()).Regin(c)
			return
		}

		// 验证关键词
		if req.Keyword == "" {
			gf.Failed().SetMsg("关键词不能为空").Regin(c)
			return
		}

		api.processSingleVideoKeyword(c, req.Keyword)

	} else if keywordType == "author" {
		// 作者类型：使用单个用户ID
		var req struct {
			UserID string `json:"user_id" binding:"required"`
		}

		if err := c.ShouldBindJSON(&req); err != nil {
			gf.Failed().SetMsg("参数错误: " + err.Error()).Regin(c)
			return
		}

		// 验证用户ID
		if req.UserID == "" {
			gf.Failed().SetMsg("用户ID不能为空").Regin(c)
			return
		}

		api.processSingleAuthorKeyword(c, req.UserID)

	} else {
		gf.Failed().SetMsg("不支持的关键词类型: " + keywordType).Regin(c)
		return
	}
}

// processSingleVideoKeyword 处理单个视频关键词
func (api *Index) processSingleVideoKeyword(c *gf.GinCtx, keyword string) {
	// 获取当前用户信息
	userObj, exists := c.Get("user")
	if !exists {
		gf.Failed().SetMsg("用户未登录").SetCode(401).Regin(c)
		return
	}
	userUUID := string(userObj.(gf.UserObj).UserUUID)

	// 第一步：调用内部方法创建/更新关键词到 MediaCrawler 服务
	keywordSyncResp, err := api.upsertKeywordToMediaCrawler(keyword)
	if err != nil {
		gf.Failed().SetMsg(fmt.Sprintf("MediaCrawler服务调用失败: %s", err.Error())).Regin(c)
		return
	}

	// 获取关键词ID
	var keywordId string
	if keywordSyncResp != nil {
		// 尝试从 KeywordData 中获取 ID
		if keywordSyncResp.HasKeywordData() {
			keywordData := keywordSyncResp.GetKeywordData()
			// KeywordData 是一个结构体，直接获取 Id 字段
			keywordId = gconv.String(keywordData.GetId())
		}
	}

	// 如果没有获取到关键词ID，报错
	if keywordId == "" {
		gf.Failed().SetMsg("未能从MediaCrawler响应中获取关键词ID").Regin(c)
		return
	}

	// 第二步：创建用户关键词关联记录，使用获取到的关键词ID
	userKeyword, err := api.userKeywordService.AddKeywordForUserWithType(userUUID, keywordId, string(entity.SourceTypeKeyword))
	if err != nil {
		gf.Failed().SetMsg(fmt.Sprintf("创建用户关键词关联失败: %s", err.Error())).Regin(c)
		return
	}

	// 第三步：如果关键词同步返回了视频数据，直接创建视频关联记录
	videoItems := keywordSyncResp.GetVideoItems()
	// 从 videoItems 中提取 aweme_ids
	var awemeIds []string
	for _, item := range videoItems {
		awemeIds = append(awemeIds, item.AwemeId)
	}
	if keywordSyncResp != nil && (len(videoItems) > 0 || len(awemeIds) > 0) {
		gf.Log().Info(context.Background(), "关键词同步返回了视频数据，开始创建视频关联记录", gf.Map{
			"user_uuid":         userUUID,
			"keyword":           keyword,
			"keyword_id":        keywordId,
			"aweme_ids":         awemeIds,
			"aweme_count":       len(awemeIds),
			"video_items_count": len(videoItems),
		})

		err := api.createKeywordVideoRelatedRecordsFromSync(userUUID, userKeyword.SourceId, videoItems)
		if err != nil {
			gf.Log().Error(context.Background(), "创建关键词视频关联记录失败", gf.Map{
				"user_uuid":         userUUID,
				"keyword":           keyword,
				"keyword_id":        keywordId,
				"aweme_count":       len(awemeIds),
				"video_items_count": len(videoItems),
				"error":             err.Error(),
			})
			// 不阻止主流程，继续执行搜索API
		} else {
			gf.Log().Info(context.Background(), "关键词视频关联记录创建成功", gf.Map{
				"user_uuid":         userUUID,
				"keyword":           keyword,
				"keyword_id":        keywordId,
				"aweme_count":       len(awemeIds),
				"video_items_count": len(videoItems),
			})
		}
	}

	// 第四步：调用视频搜索API获取更多视频列表（作为补充）
	gf.Log().Info(context.Background(), "开始调用视频搜索API获取更多视频", gf.Map{
		"user_uuid":  userUUID,
		"keyword":    keyword,
		"keyword_id": userKeyword.SourceId,
	})

	videoRelatedCount, err := api.searchAndCreateVideoRelatedRecords(userUUID, keyword, userKeyword.SourceId)
	if err != nil {
		// 记录错误但不阻止主流程，因为关键词已经创建成功
		gf.Log().Error(context.Background(), "搜索并创建视频关联记录失败", gf.Map{
			"user_uuid":  userUUID,
			"keyword":    keyword,
			"keyword_id": userKeyword.SourceId,
			"error":      err.Error(),
		})
	} else {
		gf.Log().Info(context.Background(), "视频搜索并创建关联记录完成", gf.Map{
			"user_uuid":           userUUID,
			"keyword":             keyword,
			"keyword_id":          userKeyword.SourceId,
			"video_related_count": videoRelatedCount,
		})
	}

	// 返回成功结果
	result := gf.Map{
		"keyword":             keyword,
		"user_keyword":        userKeyword,
		"video_related_count": videoRelatedCount,
		"message":             "视频关键词创建/更新成功",
	}

	gf.Success().SetMsg("视频关键词创建/更新成功").SetData(result).Regin(c)
}

// processSingleAuthorKeyword 处理单个作者关键词
func (api *Index) processSingleAuthorKeyword(c *gf.GinCtx, userID string) {
	// 获取当前用户信息
	userObj, exists := c.Get("user")
	if !exists {
		gf.Failed().SetMsg("用户未登录").SetCode(401).Regin(c)
		return
	}
	userUUID := string(userObj.(gf.UserObj).UserUUID)

	// 第一步：创建用户关键词关联记录（使用用户ID作为关键词）
	userKeyword, err := api.userKeywordService.AddKeywordForUserWithType(userUUID, userID, string(entity.SourceTypeAuthor))
	if err != nil {
		gf.Failed().SetMsg(fmt.Sprintf("创建用户关键词关联失败: %s", err.Error())).Regin(c)
		return
	}

	// 第二步：调用内部方法创建/更新作者信息到 MediaCrawler 服务，并创建视频关联记录
	err = api.upsertAuthorToMediaCrawler(userUUID, userID, userKeyword.SourceId)
	if err != nil {
		gf.Failed().SetMsg(fmt.Sprintf("MediaCrawler服务调用失败: %s", err.Error())).Regin(c)
		return
	}

	// 第三步：调用作者视频搜索API获取视频列表
	gf.Log().Info(context.Background(), "开始调用作者视频搜索并创建关联记录", gf.Map{
		"user_uuid":  userUUID,
		"author_id":  userID,
		"keyword_id": userKeyword.SourceId,
	})

	// videoRelatedCount, err := api.searchAuthorVideosAndCreateRelatedRecords(userUUID, userID, userKeyword.SourceId)
	// if err != nil {
	// 	// 记录错误但不阻止主流程，因为关键词已经创建成功
	// 	gf.Log().Error(context.Background(), "搜索作者视频并创建关联记录失败", gf.Map{
	// 		"user_uuid":  userUUID,
	// 		"author_id":  userID,
	// 		"keyword_id": userKeyword.SourceId,
	// 		"error":      err.Error(),
	// 	})
	// } else {
	// 	gf.Log().Info(context.Background(), "作者视频搜索并创建关联记录完成", gf.Map{
	// 		"user_uuid":           userUUID,
	// 		"author_id":           userID,
	// 		"keyword_id":          userKeyword.SourceId,
	// 		"video_related_count": videoRelatedCount,
	// 	})
	// }

	// 返回成功结果
	result := gf.Map{
		"user_id":      userID,
		"user_keyword": userKeyword,
		// "video_related_count": videoRelatedCount,
		"message": "作者关键词创建/更新成功",
	}

	gf.Success().SetMsg("作者关键词创建/更新成功").SetData(result).Regin(c)
}

// processAuthorKeywords 处理作者关键词（批量处理，保留兼容性）
func (api *Index) processAuthorKeywords(c *gf.GinCtx, userIDs []string) {
	// 处理结果统计
	result := gf.Map{
		"success_count": 0,
		"failed_count":  0,
		"success_users": []string{},
		"failed_users":  []gf.Map{},
	}

	// 获取当前用户信息
	userObj, exists := c.Get("user")
	if !exists {
		gf.Failed().SetMsg("用户未登录").SetCode(401).Regin(c)
		return
	}
	userUUID := string(userObj.(gf.UserObj).UserUUID)

	// 处理每个用户ID
	for _, userID := range userIDs {
		// 第一步：创建用户关键词关联记录（使用用户ID作为关键词）
		userKeyword, err := api.userKeywordService.AddKeywordForUserWithType(userUUID, userID, string(entity.SourceTypeAuthor))
		if err != nil {
			// 如果用户关联创建失败，记录错误但不影响其他用户ID
			result["failed_count"] = result["failed_count"].(int) + 1
			result["failed_users"] = append(result["failed_users"].([]gf.Map), gf.Map{
				"user_id": userID,
				"error":   fmt.Sprintf("创建用户关键词关联失败: %s", err.Error()),
			})
			continue
		}

		// 第二步：调用内部方法创建/更新作者信息到 MediaCrawler 服务，并创建视频关联记录
		err = api.upsertAuthorToMediaCrawler(userUUID, userID, userKeyword.SourceId)
		if err != nil {
			// 记录失败的用户ID
			result["failed_count"] = result["failed_count"].(int) + 1
			result["failed_users"] = append(result["failed_users"].([]gf.Map), gf.Map{
				"user_id": userID,
				"error":   fmt.Sprintf("MediaCrawler服务调用失败: %s", err.Error()),
			})
			continue
		}

		// 记录成功的用户ID
		result["success_count"] = result["success_count"].(int) + 1
		result["success_users"] = append(result["success_users"].([]string), userID)
	}

	// 返回结果
	gf.Success().SetMsg("author关键词创建/更新处理完成").SetData(result).Regin(c)
}

// GetVideoKeywordStats 获取用户视频关键词统计信息
// @Summary 获取用户视频关键词统计信息
// @Description 获取当前用户的视频关键词统计信息，包括关键词总数、活跃关键词数、视频总数、今日新增数等聚合统计数据
// @Tags 关键词统计
// @Accept json
// @Produce json
// @Success 200 {object} object "成功响应"
// @Success 200 {object} object{code=int,msg=string,data=object{keyword_count=int,active_keywords=int,total_videos=int,today_count=int,avg_videos_per_keyword=number,last_updated=string}} "成功响应示例"
// @Failure 401 {object} object{code=int,msg=string,data=object{}} "未授权"
// @Failure 500 {object} object{code=int,msg=string,data=object{}} "服务器内部错误"
// @Security BearerAuth
// @Router /client/trendinsight/index/GetVideoKeywordStats [get]
func (api *Index) GetVideoKeywordStats(c *gf.GinCtx) {
	api.getKeywordStatsByType(c, string(entity.SourceTypeKeyword))
}

// GetAuthorKeywordStats 获取用户作者关键词统计信息
// @Summary 获取用户作者关键词统计信息
// @Description 获取当前用户的作者关键词统计信息，包括关键词总数、活跃关键词数、作者总数、今日新增数等聚合统计数据
// @Tags 关键词统计
// @Accept json
// @Produce json
// @Success 200 {object} object "成功响应"
// @Success 200 {object} object{code=int,msg=string,data=object{keyword_count=int,active_keywords=int,total_authors=int,today_count=int,avg_authors_per_keyword=number,last_updated=string}} "成功响应示例"
// @Failure 401 {object} object{code=int,msg=string,data=object{}} "未授权"
// @Failure 500 {object} object{code=int,msg=string,data=object{}} "服务器内部错误"
// @Security BearerAuth
// @Router /client/trendinsight/index/GetAuthorKeywordStats [get]
func (api *Index) GetAuthorKeywordStats(c *gf.GinCtx) {
	api.getKeywordStatsByType(c, string(entity.SourceTypeAuthor))
}

// getKeywordStatsByType 通用的获取关键词统计信息方法（内部使用）
func (api *Index) getKeywordStatsByType(c *gf.GinCtx, keywordType string) {
	// 获取用户UUID
	userObj, exists := c.Get("user")
	if !exists {
		gf.Failed().SetMsg("用户未登录").SetCode(401).Regin(c)
		return
	}
	userUUID := string(userObj.(gf.UserObj).UserUUID)

	// 1. 从 user_inbox_source_related 获取关键词数量
	keywordCount, err := gf.Model("user_inbox_source_related").
		Where("user_uuid", userUUID).
		Where("source_type", keywordType).
		Where("deleted_at", "").
		Count()
	if err != nil {
		gf.Failed().SetMsg(fmt.Sprintf("查询%s关键词数量失败: %s", keywordType, err.Error())).Regin(c)
		return
	}

	// 2. 从 user_inbox_video_related 获取视频总数（按 user_uuid 和 source_type 查询）
	totalCount, err := gf.Model("user_inbox_video_related").
		Where("user_uuid", userUUID).
		Where("source_type", keywordType).
		Where("is_deleted", 0).
		Count()
	if err != nil {
		gf.Failed().SetMsg(fmt.Sprintf("查询%s总数失败: %s", keywordType, err.Error())).Regin(c)
		return
	}

	// 3. 按 publish_time 获取今日新增数量
	today := time.Now()
	todayStart := time.Date(today.Year(), today.Month(), today.Day(), 0, 0, 0, 0, today.Location())
	todayEnd := todayStart.Add(24 * time.Hour)

	todayCount, err := gf.Model("user_inbox_video_related").
		Where("user_uuid", userUUID).
		Where("source_type", keywordType).
		Where("is_deleted", 0).
		Where("publish_time >= ?", todayStart).
		Where("publish_time < ?", todayEnd).
		Count()
	if err != nil {
		gf.Failed().SetMsg(fmt.Sprintf("查询今日新增%s数量失败: %s", keywordType, err.Error())).Regin(c)
		return
	}

	// 4. 获取最后一条 user_inbox_video_related 数据
	var lastRecord entity.UserInboxVideoRelated
	// 先检查是否存在符合条件的记录
	recordCount, err := gf.Model("user_inbox_video_related").
		Where("user_uuid", userUUID).
		Where("source_type", keywordType).
		Where("is_deleted", 0).
		Count()
	if err != nil {
		gf.Failed().SetMsg(fmt.Sprintf("查询%s记录数量失败: %s", keywordType, err.Error())).Regin(c)
		return
	}

	// 如果有记录，则查询最后一条
	if recordCount > 0 {
		err = gf.Model("user_inbox_video_related").
			Where("user_uuid", userUUID).
			Where("source_type", keywordType).
			Where("is_deleted", 0).
			Order("create_time DESC, uuid DESC").
			Limit(1).
			Scan(&lastRecord)
		if err != nil {
			gf.Failed().SetMsg(fmt.Sprintf("查询最后一条%s记录失败: %s", keywordType, err.Error())).Regin(c)
			return
		}
	}
	// 如果没有记录，lastRecord 保持零值状态

	// 5. 计算活跃数据源数量（有视频数据的关键词/作者）
	activeSources, err := gf.Model("user_inbox_source_related").
		Where("user_uuid", userUUID).
		Where("source_type", keywordType).
		Where("deleted_at", "").
		Where("source_id IN (SELECT DISTINCT source_id FROM user_inbox_video_related WHERE user_uuid = ? AND source_type = ? AND is_deleted = 0)", userUUID, keywordType).
		Count()
	if err != nil {
		// 如果查询失败，使用数据源总数作为活跃数量
		activeSources = keywordCount
	}

	// 构建统计响应
	result := gf.Map{
		"keyword_count":  keywordCount,                // 关键词总数
		"active_sources": activeSources,               // 有数据的数据源数
		"today_count":    todayCount,                  // 今日新增数量
		"last_record":    lastRecord.GetDisplayInfo(), // 最后一条记录（格式化）
	}

	if keywordType == string(entity.SourceTypeKeyword) {
		result["total_videos"] = totalCount
		result["avg_videos_per_keyword"] = float64(totalCount) / float64(max(int(keywordCount), 1))
	} else {
		result["total_authors"] = totalCount
		result["avg_authors_per_keyword"] = float64(totalCount) / float64(max(int(keywordCount), 1))
	}

	gf.Success().SetMsg(fmt.Sprintf("获取%s关键词统计信息成功", keywordType)).SetData(result).Regin(c)
}

// max 辅助函数
func max(a, b int) int {
	if a > b {
		return a
	}
	return b
}

// getTodayCountByKeyword 获取指定关键词今日新增的视频或作者数量
// 注意：使用 user_inbox_video_related 表进行查询，使用 publish_time 字段
func (api *Index) getTodayCountByKeyword(keyword, dataType string) (int, error) {
	// 获取今日开始时间
	today := time.Now().Format("2006-01-02")
	todayStart := today + " 00:00:00"

	var count int
	var err error

	if dataType == string(entity.SourceTypeKeyword) {
		// 查询今日发布的视频数量（使用 user_inbox_video_related 表和 publish_time 字段）
		count, err = gf.Model("user_inbox_video_related").
			Where("source_id", keyword).
			Where("source_type", string(entity.SourceTypeKeyword)).
			Where("publish_time >= ?", todayStart).
			Where("is_deleted", 0).
			Count()
	} else if dataType == string(entity.SourceTypeAuthor) {
		// 查询今日发布的作者视频数量（使用 user_inbox_video_related 表和 publish_time 字段）
		count, err = gf.Model("user_inbox_video_related").
			Where("source_id", keyword).
			Where("source_type", string(entity.SourceTypeAuthor)).
			Where("publish_time >= ?", todayStart).
			Where("is_deleted", 0).
			Count()
	} else {
		return 0, fmt.Errorf("不支持的数据类型: %s", dataType)
	}

	if err != nil {
		return 0, fmt.Errorf("查询今日%s数量失败: %v", dataType, err)
	}

	return count, nil
}

// getKeywordCountFromService 获取指定关键词的总数量（视频或作者）
// 注意：使用 user_inbox_video_related 表进行查询
func (api *Index) getKeywordCountFromService(keyword, dataType string) (int, error) {
	var count int
	var err error

	if dataType == string(entity.SourceTypeKeyword) {
		// 查询关键词关联的视频总数量（使用 user_inbox_video_related 表）
		count, err = gf.Model("user_inbox_video_related").
			Where("source_id", keyword).
			Where("source_type", string(entity.SourceTypeKeyword)).
			Where("is_deleted", 0).
			Count()
	} else if dataType == string(entity.SourceTypeAuthor) {
		// 查询关键词关联的作者视频总数量（使用 user_inbox_video_related 表）
		count, err = gf.Model("user_inbox_video_related").
			Where("source_id", keyword).
			Where("source_type", string(entity.SourceTypeAuthor)).
			Where("is_deleted", 0).
			Count()
	} else {
		return 0, fmt.Errorf("不支持的数据类型: %s", dataType)
	}

	if err != nil {
		return 0, fmt.Errorf("查询%s数量失败: %v", dataType, err)
	}

	return count, nil
}

// upsertKeywordToMediaCrawler 调用 MediaCrawler 服务的关键词同步接口
// 这是一个内部方法，用于确保关键词在 trendinsight_keyword 表中存在
//
// 功能说明：
// - 调用 MediaCrawler 服务的 /api/v1/trendinsight/keywords/sync 接口
// - 如果关键词不存在则创建，如果存在则更新
// - 同时同步关键词的视频数据并创建关联记录
// - 返回完整的响应数据，包含关键词ID和同步的视频信息
//
// 参数：
// - keyword: 要创建/更新的关键词文本
//
// 返回值：
// - *client.KeywordSyncResponse: MediaCrawler API 的完整响应数据
// - error: 操作失败时返回错误，成功时返回 nil
//
// 响应数据包含：
// - KeywordId: 关键词在数据库中的ID
// - KeywordData: 关键词的详细信息
// - AwemeIds: 同步的视频ID列表
// - VideosSynced: 同步的视频数量等统计信息
func (api *Index) upsertKeywordToMediaCrawler(keyword string) (*client.KeywordSyncResponse, error) {
	// 创建客户端
	apiClient, err := api.createTrendInsightClient()
	if err != nil {
		return nil, fmt.Errorf("创建TrendInsight客户端失败: %w", err)
	}

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// 构建请求数据
	requestSchema := client.NewKeywordSyncRequest(keyword)

	gf.Log().Info(context.Background(), "准备发送关键词同步请求", gf.Map{
		"keyword": keyword,
	})

	// 发送请求
	resp, httpResp, err := apiClient.TrendInsightAPIAPI.SyncKeywordVideos(ctx).
		KeywordSyncRequest(*requestSchema).
		Execute()

	if err != nil {
		return nil, fmt.Errorf("发送关键词同步请求失败: %w", err)
	}

	gf.Log().Info(context.Background(), "MediaCrawler关键词同步响应", gf.Map{
		"keyword":     keyword,
		"status_code": httpResp.StatusCode,
		"response":    resp,
	})

	// 检查响应
	if resp != nil && len(resp.GetErrors()) > 0 {
		return nil, fmt.Errorf("关键词同步操作失败: %v", resp.GetErrors())
	}

	operation := "unknown"
	message := "操作成功"
	keywordId := int64(0)
	if resp != nil {
		operation = string(resp.GetKeywordAction())
		message = fmt.Sprintf("同步成功，视频数量: %d", resp.GetVideosSynced())

		// 获取关键词ID
		if resp.HasKeywordData() {
			keywordData := resp.GetKeywordData()
			// KeywordData 是一个结构体，直接获取 Id 字段
			keywordId = keywordData.GetId()
		}
	}

	// 从 videoItems 中提取 aweme_ids 用于日志
	var awemeIds []string
	if resp != nil {
		videoItems := resp.GetVideoItems()
		for _, item := range videoItems {
			awemeIds = append(awemeIds, item.AwemeId)
		}
	}

	gf.Log().Info(context.Background(), "关键词同步成功", gf.Map{
		"keyword":    keyword,
		"keyword_id": keywordId,
		"operation":  operation,
		"message":    message,
		"aweme_ids":  awemeIds,
	})

	return resp, nil
}

// upsertAuthorToMediaCrawler 调用 MediaCrawler 服务的作者同步接口
// 这是一个内部方法，用于确保作者信息在 trendinsight_author 表中存在
//
// 功能说明：
// - 调用 MediaCrawler 服务的 /api/v1/trendinsight/author/sync 接口
// - 如果作者不存在则创建，如果存在则更新
// - 同时同步作者的视频数据并创建关联记录
// - 将返回的 aweme_id 存入 user_inbox_video_related 表中
//
// 参数：
// - userUUID: 用户UUID（用于创建视频关联记录）
// - userID: 作者的用户ID（TrendInsight 平台唯一标识）
// - authorKeywordId: 作者关键词ID（作为source_id使用）
//
// 返回值：
// - error: 操作失败时返回错误，成功时返回 nil
func (api *Index) upsertAuthorToMediaCrawler(userUUID, userID, authorKeywordId string) error {
	gf.Log().Info(context.Background(), "准备调用作者同步接口", gf.Map{
		"user_id": userID,
	})

	// 创建 MediaCrawler 客户端
	apiClient, err := api.createMediaCrawlerClient()
	if err != nil {
		gf.Log().Error(context.Background(), "创建MediaCrawler客户端失败", gf.Map{
			"user_id": userID,
			"error":   err.Error(),
		})
		return fmt.Errorf("创建MediaCrawler客户端失败: %w", err)
	}

	ctx, cancel := context.WithTimeout(context.Background(), 60*time.Second) // 增加超时时间，因为同步操作可能较慢
	defer cancel()

	// 构建作者同步请求
	authorSyncRequest := client.NewAuthorSyncRequest(userID)

	gf.Log().Info(context.Background(), "开始调用作者同步API", gf.Map{
		"user_id": userID,
		"request": authorSyncRequest,
	})

	// 调用作者同步接口
	resp, httpResp, err := apiClient.TrendInsightAPIAPI.SyncAuthorVideos(ctx).
		AuthorSyncRequest(*authorSyncRequest).
		Execute()

	if err != nil {
		gf.Log().Error(context.Background(), "作者同步API调用失败", gf.Map{
			"user_id": userID,
			"error":   err.Error(),
		})
		return fmt.Errorf("作者同步请求失败: %w", err)
	}

	gf.Log().Info(context.Background(), "作者同步API调用成功", gf.Map{
		"user_id":            userID,
		"status_code":        httpResp.StatusCode,
		"author_action":      resp.GetAuthorAction(),
		"videos_synced":      resp.GetVideosSynced(),
		"videos_failed":      resp.GetVideosFailed(),
		"relations_created":  resp.GetRelationsCreated(),
		"relations_existing": resp.GetRelationsExisting(),
		"aweme_ids_count":    len(resp.GetAwemeIds()),
	})

	// 检查响应中的错误
	if resp != nil && len(resp.GetErrors()) > 0 {
		errors := resp.GetErrors()

		// 过滤掉已知的 platform 过滤错误（MediaCrawler 服务的已知问题）
		var filteredErrors []string
		var ignoredErrors []string

		for _, err := range errors {
			if strings.Contains(err, "Unknown filter param 'platform'") &&
				strings.Contains(err, "更新作者视频计数失败") {
				// 这是已知的 MediaCrawler 服务问题，记录但不阻止流程
				ignoredErrors = append(ignoredErrors, err)
			} else {
				// 其他错误需要处理
				filteredErrors = append(filteredErrors, err)
			}
		}

		// 记录所有错误信息
		gf.Log().Error(context.Background(), "作者同步操作包含错误", gf.Map{
			"user_id":         userID,
			"all_errors":      errors,
			"filtered_errors": filteredErrors,
			"ignored_errors":  ignoredErrors,
		})

		// 如果有已知错误被忽略，记录警告
		if len(ignoredErrors) > 0 {
			gf.Log().Warning(context.Background(), "作者同步包含已知的platform过滤错误，已忽略继续执行", gf.Map{
				"user_id":        userID,
				"ignored_count":  len(ignoredErrors),
				"ignored_errors": ignoredErrors,
			})
		}

		// 只有在有其他严重错误时才返回错误
		if len(filteredErrors) > 0 {
			return fmt.Errorf("作者同步操作失败: %v", filteredErrors)
		}

		// 如果只有已知的 platform 错误，记录信息但继续执行
		if len(ignoredErrors) > 0 && len(filteredErrors) == 0 {
			gf.Log().Info(context.Background(), "作者同步完成，已忽略已知的platform过滤错误", gf.Map{
				"user_id": userID,
				"message": "MediaCrawler服务存在已知的platform字段过滤问题，但不影响主要功能",
			})
		}
	}

	// 记录同步结果
	operation := resp.GetAuthorAction()
	videosSynced := resp.GetVideosSynced()
	relationsCreated := resp.GetRelationsCreated()
	awemeIds := resp.GetAwemeIds()

	gf.Log().Info(context.Background(), "作者同步成功", gf.Map{
		"user_uuid":         userUUID,
		"user_id":           userID,
		"author_keyword_id": authorKeywordId,
		"author_action":     operation,
		"videos_synced":     videosSynced,
		"relations_created": relationsCreated,
		"aweme_ids_count":   len(awemeIds),
		"aweme_ids":         awemeIds,
	})

	// 如果有同步的视频，创建 user_inbox_video_related 记录
	videoItems := resp.GetVideoItems()
	if len(videoItems) > 0 || len(awemeIds) > 0 {
		gf.Log().Info(context.Background(), "开始创建用户收件箱视频关联记录", gf.Map{
			"user_uuid":         userUUID,
			"user_id":           userID,
			"author_keyword_id": authorKeywordId,
			"aweme_ids_count":   len(awemeIds),
			"video_items_count": len(videoItems),
		})

		err := api.createAuthorVideoRelatedRecordsFromSync(userUUID, authorKeywordId, videoItems)
		if err != nil {
			gf.Log().Error(context.Background(), "创建用户收件箱视频关联记录失败", gf.Map{
				"user_uuid":         userUUID,
				"user_id":           userID,
				"author_keyword_id": authorKeywordId,
				"aweme_ids_count":   len(awemeIds),
				"video_items_count": len(videoItems),
				"error":             err.Error(),
			})
			return fmt.Errorf("创建用户收件箱视频关联记录失败: %w", err)
		}

		gf.Log().Info(context.Background(), "用户收件箱视频关联记录创建成功", gf.Map{
			"user_uuid":         userUUID,
			"user_id":           userID,
			"author_keyword_id": authorKeywordId,
			"aweme_ids_count":   len(awemeIds),
			"video_items_count": len(videoItems),
		})
	} else {
		gf.Log().Info(context.Background(), "作者同步未返回视频数据，跳过创建视频关联记录", gf.Map{
			"user_uuid":         userUUID,
			"user_id":           userID,
			"author_keyword_id": authorKeywordId,
		})
	}

	return nil
}

// DebugUserKeywords 调试用户关键词关联问题的接口
func (api *Index) DebugUserKeywords(c *gf.GinCtx) {
	param, _ := gf.RequestParam(c)
	keywordType := string(entity.SourceTypeKeyword) // 默认类型
	if typeParam, ok := param["type"]; ok && typeParam != nil {
		if typeStr, ok := typeParam.(string); ok {
			keywordType = typeStr
		}
	}

	// 使用调试服务
	debugService := services.NewDebugUserKeywordService()

	// 调试数据库连接
	if err := debugService.DebugDatabaseConnections(); err != nil {
		gf.Failed().SetMsg(fmt.Sprintf("数据库连接调试失败: %s", err.Error())).Regin(c)
		return
	}

	// 检查表结构和数据
	result := gf.Map{
		"keyword_type": keywordType,
		"debug_info": gf.Map{
			"database_connection": "成功",
			"message":             "数据库连接正常，可以进行进一步的用户关键词调试",
		},
	}

	// 如果有用户登录，则进行用户相关的调试
	userObj, exists := c.Get("user")
	if exists {
		userUUID := string(userObj.(gf.UserObj).UserUUID)

		// 调试特定用户的关键词
		if err := debugService.DebugSpecificUserKeywords(userUUID); err != nil {
			result["user_debug_error"] = err.Error()
		} else {
			// 调试获取用户关键词
			userKeywords, total, err := debugService.DebugGetUserKeywordsByType(userUUID, keywordType, 1, 10)
			if err != nil {
				result["user_keywords_error"] = err.Error()
			} else {
				result["total"] = total
				result["keywords"] = userKeywords
				result["debug_info"].(gf.Map)["user_uuid"] = userUUID
				result["debug_info"].(gf.Map)["found_records"] = len(userKeywords)
				result["debug_info"].(gf.Map)["total_records"] = total
			}
		}
	} else {
		result["debug_info"].(gf.Map)["user_status"] = "未登录，仅进行数据库连接测试"
	}

	gf.Success().SetMsg("调试信息获取成功").SetData(result).Regin(c)
}

// TestDatabaseData 测试数据库数据的接口
func (api *Index) TestDatabaseData(c *gf.GinCtx) {
	result := gf.Map{}

	// 测试 user_inbox_source_related 表
	count1, err1 := gf.Model("user_inbox_source_related").Count()
	if err1 != nil {
		result["user_inbox_source_related_error"] = err1.Error()
	} else {
		result["user_inbox_source_related_count"] = count1
	}

	// 测试 trendinsight_keyword 表
	count2, err2 := setting.CrawlerModel("trendinsight_keyword").Count()
	if err2 != nil {
		result["trendinsight_keyword_error"] = err2.Error()
	} else {
		result["trendinsight_keyword_count"] = count2
	}

	// 获取一些示例数据 - 先不加条件查看所有数据
	var sampleRecords []*entity.UserInboxSourceRelated
	err3 := gf.Model("user_inbox_source_related").
		Limit(5).
		Scan(&sampleRecords)

	if err3 != nil {
		result["sample_records_error"] = err3.Error()
	} else {
		result["sample_records"] = sampleRecords
		result["sample_records_count"] = len(sampleRecords)
	}

	// 检查表结构
	var tableInfo []map[string]interface{}
	err4 := gf.Model("user_inbox_source_related").
		Limit(1).
		Scan(&tableInfo)

	if err4 != nil {
		result["table_structure_error"] = err4.Error()
	} else if len(tableInfo) > 0 {
		result["table_structure_sample"] = tableInfo[0]
	}

	gf.Success().SetMsg("数据库测试完成").SetData(result).Regin(c)
}

// searchAndCreateVideoRelatedRecords 搜索视频并创建用户收件箱视频关联记录
// 参数：
// - userUUID: 用户UUID
// - keyword: 搜索关键词
// - keywordId: 关键词ID（作为source_id使用）
// 返回值：
// - int: 创建的视频关联记录数量
// - error: 错误信息
func (api *Index) searchAndCreateVideoRelatedRecords(userUUID, keyword, keywordId string) (int, error) {
	gf.Log().Info(context.Background(), "=== 进入 searchAndCreateVideoRelatedRecords 函数 ===", gf.Map{
		"user_uuid":  userUUID,
		"keyword":    keyword,
		"keyword_id": keywordId,
	})

	// 创建客户端
	gf.Log().Info(context.Background(), "步骤1: 创建TrendInsight客户端", gf.Map{
		"user_uuid":  userUUID,
		"keyword":    keyword,
		"keyword_id": keywordId,
	})
	apiClient, err := api.createTrendInsightClient()
	if err != nil {
		gf.Log().Error(context.Background(), "创建TrendInsight客户端失败", gf.Map{
			"user_uuid":  userUUID,
			"keyword":    keyword,
			"keyword_id": keywordId,
			"error":      err.Error(),
		})
		return 0, fmt.Errorf("创建TrendInsight客户端失败: %w", err)
	}
	gf.Log().Info(context.Background(), "TrendInsight客户端创建成功", gf.Map{
		"user_uuid":  userUUID,
		"keyword":    keyword,
		"keyword_id": keywordId,
	})

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// 构建搜索请求
	gf.Log().Info(context.Background(), "步骤2: 构建搜索请求", gf.Map{
		"user_uuid": userUUID,
		"keyword":   keyword,
	})
	searchReq := apiClient.TrendInsightAPIAPI.SearchVideosByKeyword(ctx).
		Keyword(keyword)

	gf.Log().Info(ctx, "步骤3: 开始执行视频搜索API调用", gf.Map{
		"user_uuid": userUUID,
		"keyword":   keyword,
	})

	// 执行搜索请求
	resp, httpResp, err := searchReq.Execute()
	if err != nil {
		gf.Log().Error(context.Background(), "视频搜索API调用失败", gf.Map{
			"user_uuid": userUUID,
			"keyword":   keyword,
			"error":     err.Error(),
		})
		return 0, fmt.Errorf("视频搜索请求失败: %w", err)
	}

	gf.Log().Info(context.Background(), "视频搜索API调用成功", gf.Map{
		"user_uuid":     userUUID,
		"keyword":       keyword,
		"http_status":   httpResp.StatusCode,
		"response_size": len(fmt.Sprintf("%+v", resp)),
	})

	// 检查响应
	if resp == nil {
		gf.Log().Error(context.Background(), "搜索响应为空", gf.Map{
			"user_uuid": userUUID,
			"keyword":   keyword,
		})
		return 0, fmt.Errorf("搜索响应为空")
	}

	// 获取视频数据
	videos := resp.GetData().Data

	// 检查是否有视频数据
	if len(videos) == 0 {
		gf.Log().Info(ctx, "关键词搜索未找到视频", gf.Map{
			"user_uuid": userUUID,
			"keyword":   keyword,
		})
		return 0, nil
	}

	gf.Log().Info(context.Background(), "步骤4: 搜索到视频数据", gf.Map{
		"user_uuid":   userUUID,
		"keyword":     keyword,
		"video_count": len(videos),
	})

	// 提取视频的 aweme_id 列表
	gf.Log().Info(context.Background(), "步骤5: 开始提取视频aweme_id", gf.Map{
		"user_uuid":   userUUID,
		"keyword":     keyword,
		"video_count": len(videos),
	})

	var awemeIds []string
	var videoInfoMap = make(map[string]map[string]interface{})
	var skippedCount int

	for i, video := range videos {
		// 获取视频ID
		var awemeId string
		if video.GetItemId() != "" {
			awemeId = video.GetItemId()
		}

		if awemeId == "" {
			skippedCount++
			gf.Log().Warning(context.Background(), "跳过没有有效ID的视频", gf.Map{
				"user_uuid":   userUUID,
				"keyword":     keyword,
				"video_index": i,
				"item_id":     video.GetItemId(),
			})
			continue // 跳过没有有效ID的视频
		}

		awemeIds = append(awemeIds, awemeId)

		gf.Log().Debug(context.Background(), "提取到视频ID", gf.Map{
			"user_uuid":   userUUID,
			"keyword":     keyword,
			"video_index": i,
			"aweme_id":    awemeId,
		})

		// 保存视频信息用于后续设置发布时间
		videoInfo := map[string]interface{}{
			"aweme_id": awemeId,
		}

		// 处理发布时间
		if video.GetCreateTime() != "" {
			// 尝试解析时间字符串
			if publishTime, err := time.Parse("2006-01-02 15:04:05", video.GetCreateTime()); err == nil {
				videoInfo["publish_time"] = publishTime
			} else {
				videoInfo["publish_time"] = time.Now() // 解析失败时使用当前时间
			}
		} else {
			videoInfo["publish_time"] = time.Now() // 默认当前时间
		}

		// 保存其他有用信息
		if video.GetTitle() != "" {
			videoInfo["title"] = video.GetTitle()
		}
		if video.GetNickname() != "" {
			videoInfo["author_name"] = video.GetNickname()
		}

		videoInfoMap[awemeId] = videoInfo
	}

	gf.Log().Info(context.Background(), "步骤6: 视频ID提取完成", gf.Map{
		"user_uuid":       userUUID,
		"keyword":         keyword,
		"total_videos":    len(videos),
		"valid_aweme_ids": len(awemeIds),
		"skipped_count":   skippedCount,
		"aweme_ids":       awemeIds,
	})

	if len(awemeIds) == 0 {
		gf.Log().Info(ctx, "关键词搜索的视频中没有有效的aweme_id", gf.Map{
			"user_uuid":     userUUID,
			"keyword":       keyword,
			"video_count":   len(videos),
			"skipped_count": skippedCount,
		})
		return 0, nil
	}

	// 创建 UserInboxVideoRelatedService 实例
	gf.Log().Info(context.Background(), "步骤7: 创建UserInboxVideoRelatedService实例", gf.Map{
		"user_uuid": userUUID,
		"keyword":   keyword,
	})
	videoRelatedService := services.NewUserInboxVideoRelatedService()

	// 查询当前用户已存在的记录，避免重复创建
	gf.Log().Info(context.Background(), "步骤8: 查询已存在的记录", gf.Map{
		"user_uuid":       userUUID,
		"keyword":         keyword,
		"check_aweme_ids": awemeIds,
	})
	existingAwemeIds, err := videoRelatedService.GetExistingAwemeIds(userUUID, awemeIds)
	if err != nil {
		gf.Log().Error(context.Background(), "检查已存在记录失败", gf.Map{
			"user_uuid": userUUID,
			"keyword":   keyword,
			"error":     err.Error(),
		})
		return 0, fmt.Errorf("检查已存在记录失败: %w", err)
	}

	gf.Log().Info(context.Background(), "步骤9: 已存在记录查询完成", gf.Map{
		"user_uuid":          userUUID,
		"keyword":            keyword,
		"total_aweme_ids":    len(awemeIds),
		"existing_aweme_ids": len(existingAwemeIds),
		"existing_ids":       existingAwemeIds,
	})

	// 过滤出尚未存在的视频记录
	existingMap := make(map[string]bool)
	for _, id := range existingAwemeIds {
		existingMap[id] = true
	}

	var newAwemeIds []string
	for _, awemeId := range awemeIds {
		if !existingMap[awemeId] {
			newAwemeIds = append(newAwemeIds, awemeId)
		}
	}

	gf.Log().Info(context.Background(), "步骤10: 过滤新视频记录完成", gf.Map{
		"user_uuid":      userUUID,
		"keyword":        keyword,
		"total_videos":   len(awemeIds),
		"existing_count": len(existingAwemeIds),
		"new_count":      len(newAwemeIds),
		"new_aweme_ids":  newAwemeIds,
	})

	if len(newAwemeIds) == 0 {
		gf.Log().Info(ctx, "所有视频记录已存在，无需创建", gf.Map{
			"user_uuid":      userUUID,
			"keyword":        keyword,
			"total_videos":   len(awemeIds),
			"existing_count": len(existingAwemeIds),
		})
		return 0, nil
	}

	// 为新视频创建 user_inbox_video_related 记录
	gf.Log().Info(context.Background(), "步骤11: 开始创建视频关联记录", gf.Map{
		"user_uuid":     userUUID,
		"keyword":       keyword,
		"keyword_id":    keywordId,
		"new_count":     len(newAwemeIds),
		"new_aweme_ids": newAwemeIds,
	})

	err = api.createVideoRelatedRecords(userUUID, keywordId, newAwemeIds, videoInfoMap)
	if err != nil {
		gf.Log().Error(context.Background(), "创建视频关联记录失败", gf.Map{
			"user_uuid":     userUUID,
			"keyword":       keyword,
			"keyword_id":    keywordId,
			"new_count":     len(newAwemeIds),
			"new_aweme_ids": newAwemeIds,
			"error":         err.Error(),
		})
		return 0, fmt.Errorf("创建视频关联记录失败: %w", err)
	}

	gf.Log().Info(context.Background(), "步骤12: 视频关联记录创建成功", gf.Map{
		"user_uuid":      userUUID,
		"keyword":        keyword,
		"total_videos":   len(awemeIds),
		"existing_count": len(existingAwemeIds),
		"new_count":      len(newAwemeIds),
	})

	gf.Log().Info(context.Background(), "=== searchAndCreateVideoRelatedRecords 函数执行完成 ===", gf.Map{
		"user_uuid":          userUUID,
		"keyword":            keyword,
		"final_result_count": len(newAwemeIds),
		"execution_status":   "SUCCESS",
	})

	return len(newAwemeIds), nil
}

// createVideoRelatedRecords 创建用户收件箱视频关联记录
// 参数：
// - userUUID: 用户UUID
// - keywordId: 关键词ID（作为source_id）
// - awemeIds: 视频ID列表
// - videoInfoMap: 视频信息映射
func (api *Index) createVideoRelatedRecords(userUUID, keywordId string, awemeIds []string, videoInfoMap map[string]map[string]interface{}) error {
	gf.Log().Info(context.Background(), "=== 进入 createVideoRelatedRecords 函数 ===", gf.Map{
		"user_uuid":   userUUID,
		"keyword_id":  keywordId,
		"aweme_ids":   awemeIds,
		"aweme_count": len(awemeIds),
	})

	if len(awemeIds) == 0 {
		gf.Log().Info(context.Background(), "awemeIds为空，无需创建记录", gf.Map{
			"user_uuid":  userUUID,
			"keyword_id": keywordId,
		})
		return nil
	}

	// 创建 UserInboxVideoRelatedService 实例
	gf.Log().Info(context.Background(), "创建UserInboxVideoRelatedService实例", gf.Map{
		"user_uuid":  userUUID,
		"keyword_id": keywordId,
	})
	videoRelatedService := services.NewUserInboxVideoRelatedService()

	// 批量创建记录
	gf.Log().Info(context.Background(), "开始构建记录数据", gf.Map{
		"user_uuid":   userUUID,
		"keyword_id":  keywordId,
		"aweme_count": len(awemeIds),
	})
	var records []*entity.UserInboxVideoRelated
	now := time.Now()

	for _, awemeId := range awemeIds {
		// 获取视频信息
		videoInfo, exists := videoInfoMap[awemeId]
		var publishTime time.Time
		if exists {
			if pt, ok := videoInfo["publish_time"].(time.Time); ok {
				publishTime = pt
			} else {
				publishTime = now
			}
		} else {
			publishTime = now
		}

		// 创建记录
		record := &entity.UserInboxVideoRelated{
			UserUUID:     userUUID,
			SourceId:     keywordId, // 使用关键词ID作为来源标识
			AwemeId:      awemeId,
			SourceType:   entity.SourceTypeKeyword, // 设置为关键词类型（表示来源于视频搜索）
			PublishTime:  publishTime,              // 视频的发布时间
			HandleStatus: cons.Pending.Value,       // 设置为 "pending"（待处理状态）
		}

		// 执行创建前钩子
		if err := record.BeforeCreate(); err != nil {
			return fmt.Errorf("记录 %s 创建前钩子失败: %w", awemeId, err)
		}

		records = append(records, record)
	}

	// 使用服务的批量插入方法
	gf.Log().Info(context.Background(), "开始执行批量插入", gf.Map{
		"user_uuid":    userUUID,
		"keyword_id":   keywordId,
		"record_count": len(records),
	})

	err := videoRelatedService.BatchInsert(records)
	if err != nil {
		gf.Log().Error(context.Background(), "批量插入视频关联记录失败", gf.Map{
			"user_uuid":    userUUID,
			"keyword_id":   keywordId,
			"record_count": len(records),
			"aweme_ids":    awemeIds,
			"error":        err.Error(),
		})
		return fmt.Errorf("批量插入视频关联记录失败: %w", err)
	}

	gf.Log().Info(context.Background(), "成功批量创建视频关联记录", gf.Map{
		"user_uuid":    userUUID,
		"keyword_id":   keywordId,
		"record_count": len(records),
		"aweme_ids":    awemeIds,
	})

	gf.Log().Info(context.Background(), "=== createVideoRelatedRecords 函数执行完成 ===", gf.Map{
		"user_uuid":        userUUID,
		"keyword_id":       keywordId,
		"created_count":    len(records),
		"execution_status": "SUCCESS",
	})

	return nil
}

// searchAuthorVideosAndCreateRelatedRecords 搜索作者视频并创建用户收件箱视频关联记录
// 参数：
// - userUUID: 用户UUID
// - authorID: 作者ID（作为 sec_user_id 使用）
// - keywordId: 关键词ID（作为source_id使用）
// 返回值：
// - int: 创建的视频关联记录数量
// - error: 错误信息
func (api *Index) searchAuthorVideosAndCreateRelatedRecords(userUUID, authorID, keywordId string) (int, error) {
	gf.Log().Info(context.Background(), "=== 进入 searchAuthorVideosAndCreateRelatedRecords 函数 ===", gf.Map{
		"user_uuid":  userUUID,
		"author_id":  authorID,
		"keyword_id": keywordId,
	})

	// 创建 MediaCrawler 客户端
	gf.Log().Info(context.Background(), "步骤1: 创建MediaCrawler客户端", gf.Map{
		"user_uuid": userUUID,
		"author_id": authorID,
	})
	// TODO: 需要找到正确的用户视频获取API
	// apiClient, err := api.createMediaCrawlerClient()
	// if err != nil {
	//	gf.Log().Error(context.Background(), "创建MediaCrawler客户端失败", gf.Map{
	//		"user_uuid": userUUID,
	//		"author_id": authorID,
	//		"error":     err.Error(),
	//	})
	//	return 0, fmt.Errorf("创建MediaCrawler客户端失败: %w", err)
	// }
	gf.Log().Info(context.Background(), "MediaCrawler客户端创建成功", gf.Map{
		"user_uuid": userUUID,
		"author_id": authorID,
	})

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// 构建作者视频获取请求（使用MediaCrawler的用户视频接口）
	gf.Log().Info(context.Background(), "步骤2: 构建作者视频获取请求", gf.Map{
		"user_uuid":   userUUID,
		"author_id":   authorID,
		"sec_user_id": authorID,
	})

	gf.Log().Info(ctx, "步骤3: 开始执行作者视频获取API调用", gf.Map{
		"user_uuid":   userUUID,
		"author_id":   authorID,
		"sec_user_id": authorID,
	})

	// TODO: 需要找到正确的用户视频获取API
	// 执行用户视频获取请求
	// resp, httpResp, err := apiClient.APIAPI.GetUserVideosRpcAutoCookies(ctx, authorID).Execute()
	gf.Log().Warning(context.Background(), "用户视频获取API暂时不可用", gf.Map{
		"user_uuid":   userUUID,
		"author_id":   authorID,
		"sec_user_id": authorID,
	})
	return 0, fmt.Errorf("用户视频获取API暂时不可用")
}

// createAuthorVideoRelatedRecords 创建作者视频关联记录
// 参数：
// - userUUID: 用户UUID
// - keywordId: 关键词ID（作为source_id）
// - awemeIds: 视频ID列表
// - videoInfoMap: 视频信息映射
func (api *Index) createAuthorVideoRelatedRecords(userUUID, keywordId string, awemeIds []string, videoInfoMap map[string]map[string]interface{}) error {
	gf.Log().Info(context.Background(), "=== 进入 createAuthorVideoRelatedRecords 函数 ===", gf.Map{
		"user_uuid":   userUUID,
		"keyword_id":  keywordId,
		"aweme_ids":   awemeIds,
		"aweme_count": len(awemeIds),
	})

	if len(awemeIds) == 0 {
		gf.Log().Info(context.Background(), "awemeIds为空，无需创建记录", gf.Map{
			"user_uuid":  userUUID,
			"keyword_id": keywordId,
		})
		return nil
	}

	// 创建 UserInboxVideoRelatedService 实例
	gf.Log().Info(context.Background(), "创建UserInboxVideoRelatedService实例", gf.Map{
		"user_uuid":  userUUID,
		"keyword_id": keywordId,
	})
	videoRelatedService := services.NewUserInboxVideoRelatedService()

	// 批量创建记录
	gf.Log().Info(context.Background(), "开始构建记录数据", gf.Map{
		"user_uuid":   userUUID,
		"keyword_id":  keywordId,
		"aweme_count": len(awemeIds),
	})
	var records []*entity.UserInboxVideoRelated
	now := time.Now()

	for _, awemeId := range awemeIds {
		// 获取视频信息
		videoInfo, exists := videoInfoMap[awemeId]
		var publishTime time.Time
		if exists {
			if pt, ok := videoInfo["publish_time"].(time.Time); ok {
				publishTime = pt
			} else {
				publishTime = now
			}
		} else {
			publishTime = now
		}

		// 创建记录
		record := &entity.UserInboxVideoRelated{
			UserUUID:     userUUID,
			SourceId:     keywordId, // 使用关键词ID作为来源标识
			AwemeId:      awemeId,
			SourceType:   "author",           // 设置为 "author"（表示来源于作者搜索）
			PublishTime:  publishTime,        // 视频的发布时间
			HandleStatus: cons.Pending.Value, // 设置为 "pending"（待处理状态）
		}

		// 执行创建前钩子
		if err := record.BeforeCreate(); err != nil {
			return fmt.Errorf("记录 %s 创建前钩子失败: %w", awemeId, err)
		}

		records = append(records, record)
	}

	// 使用服务的批量插入方法
	gf.Log().Info(context.Background(), "开始执行批量插入", gf.Map{
		"user_uuid":    userUUID,
		"keyword_id":   keywordId,
		"record_count": len(records),
	})

	err := videoRelatedService.BatchInsert(records)
	if err != nil {
		gf.Log().Error(context.Background(), "批量插入视频关联记录失败", gf.Map{
			"user_uuid":    userUUID,
			"keyword_id":   keywordId,
			"record_count": len(records),
			"aweme_ids":    awemeIds,
			"error":        err.Error(),
		})
		return fmt.Errorf("批量插入视频关联记录失败: %w", err)
	}

	gf.Log().Info(context.Background(), "成功批量创建视频关联记录", gf.Map{
		"user_uuid":    userUUID,
		"keyword_id":   keywordId,
		"record_count": len(records),
		"aweme_ids":    awemeIds,
	})

	gf.Log().Info(context.Background(), "=== createAuthorVideoRelatedRecords 函数执行完成 ===", gf.Map{
		"user_uuid":        userUUID,
		"keyword_id":       keywordId,
		"created_count":    len(records),
		"execution_status": "SUCCESS",
	})

	return nil
}

// createAuthorVideoRelatedRecordsFromSync 从作者同步结果创建用户收件箱视频关联记录
// 参数：
// - userUUID: 用户UUID
// - authorKeywordId: 作者关键词ID（作为source_id）
// - videoItems: 视频项目列表（包含详细信息）
// 返回值：
// - error: 错误信息
func (api *Index) createAuthorVideoRelatedRecordsFromSync(userUUID, authorKeywordId string, videoItems []client.DouyinAwemeData) error {
	gf.Log().Info(context.Background(), "=== 进入 createAuthorVideoRelatedRecordsFromSync 函数 ===", gf.Map{
		"user_uuid":         userUUID,
		"author_keyword_id": authorKeywordId,
		"video_items":       videoItems,
		"video_count":       len(videoItems),
	})

	if len(videoItems) == 0 {
		gf.Log().Info(context.Background(), "videoItems为空，无需创建记录", gf.Map{
			"user_uuid":         userUUID,
			"author_keyword_id": authorKeywordId,
		})
		return nil
	}

	// 从 VideoItem 中提取 aweme_id 列表和发布时间信息
	var awemeIds []string
	var videoInfoMap = make(map[string]map[string]interface{})

	for _, videoItem := range videoItems {
		awemeId := videoItem.GetAwemeId()
		if awemeId == "" {
			continue // 跳过没有有效ID的视频
		}

		awemeIds = append(awemeIds, awemeId)

		// 保存视频信息用于后续设置发布时间
		videoInfo := map[string]interface{}{
			"aweme_id": awemeId,
		}

		// 处理发布时间
		if videoItem.HasCreateTime() {
			publishTime := videoItem.GetCreateTime()
			videoInfo["publish_time"] = publishTime
		} else {
			videoInfo["publish_time"] = time.Now() // 默认当前时间
		}

		videoInfoMap[awemeId] = videoInfo
	}

	// 使用服务的批量创建方法，设置 source_type 为 "author"
	gf.Log().Info(context.Background(), "开始批量创建作者视频关联记录", gf.Map{
		"user_uuid":         userUUID,
		"author_keyword_id": authorKeywordId,
		"video_count":       len(videoItems),
		"valid_aweme_count": len(awemeIds),
	})

	// 创建 UserInboxVideoRelatedService 实例
	videoRelatedService := services.NewUserInboxVideoRelatedService()

	// 批量创建记录
	var records []*entity.UserInboxVideoRelated
	now := time.Now()

	for _, awemeId := range awemeIds {
		// 获取视频信息
		videoInfo, exists := videoInfoMap[awemeId]
		var publishTime time.Time
		if exists {
			if pt, ok := videoInfo["publish_time"].(time.Time); ok {
				publishTime = pt
			} else {
				publishTime = now
			}
		} else {
			publishTime = now
		}

		// 创建记录
		record := &entity.UserInboxVideoRelated{
			UserUUID:     userUUID,
			SourceId:     authorKeywordId, // 使用作者关键词ID作为来源标识
			AwemeId:      awemeId,
			SourceType:   entity.SourceTypeAuthor, // 设置为作者类型
			PublishTime:  publishTime,             // 视频的发布时间
			HandleStatus: cons.Pending.Value,      // 设置为 "pending"（待处理状态）
		}

		// 执行创建前钩子
		if err := record.BeforeCreate(); err != nil {
			return fmt.Errorf("记录 %s 创建前钩子失败: %w", awemeId, err)
		}

		records = append(records, record)
	}

	// 使用服务的批量插入方法
	err := videoRelatedService.BatchInsert(records)
	if err != nil {
		gf.Log().Error(context.Background(), "批量创建作者视频关联记录失败", gf.Map{
			"user_uuid":         userUUID,
			"author_keyword_id": authorKeywordId,
			"video_count":       len(videoItems),
			"valid_aweme_count": len(awemeIds),
			"error":             err.Error(),
		})
		return fmt.Errorf("批量创建作者视频关联记录失败: %w", err)
	}

	gf.Log().Info(context.Background(), "成功批量创建作者视频关联记录", gf.Map{
		"user_uuid":         userUUID,
		"author_keyword_id": authorKeywordId,
		"video_count":       len(videoItems),
		"valid_aweme_count": len(awemeIds),
	})

	gf.Log().Info(context.Background(), "=== createAuthorVideoRelatedRecordsFromSync 函数执行完成 ===", gf.Map{
		"user_uuid":         userUUID,
		"author_keyword_id": authorKeywordId,
		"created_count":     len(awemeIds),
		"execution_status":  "SUCCESS",
	})

	return nil
}

// createKeywordVideoRelatedRecordsFromSync 从关键词同步结果创建用户收件箱视频关联记录
// 参数：
// - userUUID: 用户UUID
// - keywordId: 关键词ID（作为source_id）
// - videoItems: 视频项目列表（包含详细信息）
// 返回值：
// - error: 错误信息
func (api *Index) createKeywordVideoRelatedRecordsFromSync(userUUID, keywordId string, videoItems []client.DouyinAwemeData) error {
	gf.Log().Info(context.Background(), "=== 进入 createKeywordVideoRelatedRecordsFromSync 函数 ===", gf.Map{
		"user_uuid":   userUUID,
		"keyword_id":  keywordId,
		"video_items": videoItems,
		"video_count": len(videoItems),
	})

	if len(videoItems) == 0 {
		gf.Log().Info(context.Background(), "videoItems为空，无需创建记录", gf.Map{
			"user_uuid":  userUUID,
			"keyword_id": keywordId,
		})
		return nil
	}

	// 从 VideoItem 中提取 aweme_id 列表和发布时间信息
	var awemeIds []string
	var videoInfoMap = make(map[string]map[string]interface{})

	for _, videoItem := range videoItems {
		awemeId := videoItem.GetAwemeId()
		if awemeId == "" {
			continue // 跳过没有有效ID的视频
		}

		awemeIds = append(awemeIds, awemeId)

		// 保存视频信息用于后续设置发布时间
		videoInfo := map[string]interface{}{
			"aweme_id": awemeId,
		}

		// 处理发布时间
		if videoItem.HasCreateTime() {
			publishTime := videoItem.GetCreateTime()
			videoInfo["publish_time"] = publishTime
		} else {
			videoInfo["publish_time"] = time.Now() // 默认当前时间
		}

		videoInfoMap[awemeId] = videoInfo
	}

	// 使用服务的批量创建方法，设置 source_type 为 "video"
	gf.Log().Info(context.Background(), "开始批量创建关键词视频关联记录", gf.Map{
		"user_uuid":         userUUID,
		"keyword_id":        keywordId,
		"video_count":       len(videoItems),
		"valid_aweme_count": len(awemeIds),
	})

	// 创建 UserInboxVideoRelatedService 实例
	videoRelatedService := services.NewUserInboxVideoRelatedService()

	// 批量创建记录
	var records []*entity.UserInboxVideoRelated
	now := time.Now()

	for _, awemeId := range awemeIds {
		// 获取视频信息
		videoInfo, exists := videoInfoMap[awemeId]
		var publishTime time.Time
		if exists {
			if pt, ok := videoInfo["publish_time"].(time.Time); ok {
				publishTime = pt
			} else {
				publishTime = now
			}
		} else {
			publishTime = now
		}

		// 创建记录
		record := &entity.UserInboxVideoRelated{
			UserUUID:     userUUID,
			SourceId:     keywordId, // 使用关键词ID作为来源标识
			AwemeId:      awemeId,
			SourceType:   entity.SourceTypeKeyword, // 设置为关键词类型（表示来源于关键词同步）
			PublishTime:  publishTime,              // 视频的发布时间
			HandleStatus: cons.Pending.Value,       // 设置为 "pending"（待处理状态）
		}

		// 执行创建前钩子
		if err := record.BeforeCreate(); err != nil {
			return fmt.Errorf("记录 %s 创建前钩子失败: %w", awemeId, err)
		}

		records = append(records, record)
	}

	// 使用服务的批量插入方法
	err := videoRelatedService.BatchInsert(records)
	if err != nil {
		gf.Log().Error(context.Background(), "批量创建关键词视频关联记录失败", gf.Map{
			"user_uuid":         userUUID,
			"keyword_id":        keywordId,
			"video_count":       len(videoItems),
			"valid_aweme_count": len(awemeIds),
			"error":             err.Error(),
		})
		return fmt.Errorf("批量创建关键词视频关联记录失败: %w", err)
	}

	gf.Log().Info(context.Background(), "成功批量创建关键词视频关联记录", gf.Map{
		"user_uuid":         userUUID,
		"keyword_id":        keywordId,
		"video_count":       len(videoItems),
		"valid_aweme_count": len(awemeIds),
	})

	gf.Log().Info(context.Background(), "=== createKeywordVideoRelatedRecordsFromSync 函数执行完成 ===", gf.Map{
		"user_uuid":        userUUID,
		"keyword_id":       keywordId,
		"created_count":    len(awemeIds),
		"execution_status": "SUCCESS",
	})

	return nil
}
